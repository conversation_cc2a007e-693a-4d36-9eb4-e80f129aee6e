<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_cl_listening_progress_container"
    android:layout_width="120dp"
    android:layout_height="120dp"
    android:background="@drawable/main_bg_listening_progress_floating"
    android:clickable="true"
    android:focusable="true"
    android:elevation="8dp">

    <!-- 圆形进度条 -->
    <ProgressBar
        android:id="@+id/main_pb_listening_progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:max="100"
        android:progress="0"
        android:progressDrawable="@drawable/main_progress_listening_circular"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 中心图标 -->
    <ImageView
        android:id="@+id/main_iv_listening_progress_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/main_ic_listening_progress_headphone"
        android:tint="@color/main_white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 进度文本 -->
    <TextView
        android:id="@+id/main_tv_listening_progress_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="@color/main_white"
        android:textSize="10sp"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@+id/main_iv_listening_progress_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="25%" />

</androidx.constraintlayout.widget.ConstraintLayout>
