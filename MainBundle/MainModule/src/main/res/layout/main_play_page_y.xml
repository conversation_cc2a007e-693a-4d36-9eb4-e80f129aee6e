<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.main.playpage.playy.view.ScaleTouchFrame
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_xplay_top_frame"
    android:layout_width="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <com.ximalaya.ting.android.main.playpage.playy.view.ForbiddenTouchFrame
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/main_background_with_mode"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </com.ximalaya.ting.android.main.playpage.playy.view.ForbiddenTouchFrame>

    <com.ximalaya.ting.android.main.playpage.playy.view.XPlayLayout
        android:id="@+id/main_play_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="true"
        android:orientation="vertical">

        <com.ximalaya.ting.android.main.playpage.playy.view.XCornerAppBarLayout
            android:id="@+id/main_xplay_top_area"
            android:background="@color/host_transparent"
            app:elevation="0dp"
            android:layout_height="740dp"
            android:layout_width="match_parent">

            <com.ximalaya.ting.android.main.playpage.playy.view.XCornerConstraintLayout
                android:id="@+id/main_play_top_container"
                app:layout_scrollFlags="scroll|exitUntilCollapsed"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="0dp">

                <ViewStub
                    android:id="@+id/main_x_immersive_ad_container"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    tools:visibility="visible"
                    android:visibility="gone"
                    android:layout="@layout/main_audio_play_component_immersive_skin_y"/>

                <RelativeLayout
                    android:id="@+id/main_x_cover_ad_s_container"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:visibility="gone"
                />

                <RelativeLayout
                    android:id="@+id/main_audio_container"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:clipChildren="false"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <!-- 占位，高度等于PlayFragmentNew中标题栏的高度，如果是沉浸式会算上状态栏高度，在代码中设置 -->
                    <Space
                        android:id="@+id/main_v_title_bar_placeholder"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"/>

                    <include
                        layout="@layout/main_layout_audio_play_page_normal_area_y"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@id/main_v_title_bar_placeholder"/>

                    <!-- 占位，状态栏高度，在代码中设置 -->
                    <Space
                        android:id="@+id/main_v_status_bar_placeholder"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"/>

                    <com.ximalaya.ting.android.main.playpage.playy.view.ForwardTouchFrame
                        android:id="@+id/main_play_media_pager_wrapper"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <androidx.viewpager2.widget.ViewPager2
                            android:id="@+id/main_play_media_pager"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent" />

                        <master.flame.danmaku.ui.widget.DanmakuView
                            android:id="@+id/main_view_danmaku"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                        />
                    </com.ximalaya.ting.android.main.playpage.playy.view.ForwardTouchFrame>


                    <com.ximalaya.ting.android.main.playpage.playy.view.PlayTypeIndicator
                        android:id="@+id/main_play_type_indicator"
                        android:layout_width="93dp"
                        android:layout_height="26dp"
                        android:layout_marginTop="10dp"
                        android:layout_centerHorizontal="true"
                        android:layout_below="@id/main_v_title_bar_placeholder" />

                    <!--仅好友可听-->
                    <ViewStub
                        android:id="@+id/main_xcover_friend_only"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@id/main_v_title_bar_placeholder"
                        android:layout_above="@id/main_rv_func_root"
                        android:layout="@layout/main_layout_friend_only_visible_y"/>

                    <ViewStub
                        android:id="@+id/main_cover_mot_card"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@id/main_v_title_bar_placeholder"
                        android:layout_above="@+id/main_vg_ypage_tips_container"
                        android:layout="@layout/main_mot_recommend_y_layout"/>

                    <ViewStub
                        android:id="@+id/main_vs_chatxmly_bobo_card"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_above="@id/main_vg_ypage_tips_container"
                        android:layout_below="@id/main_v_title_bar_placeholder"
                        android:layout="@layout/main_layout_chatxmly_bobo_card"/>

                    <include
                        android:id="@+id/main_vg_ypage_tips_container"
                        layout="@layout/main_layout_tips_container_y"
                        android:layout_marginBottom="22dp"
                        android:layout_above="@id/main_rv_func_root"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                    <!--高频功能-->
                    <RelativeLayout
                        android:id="@+id/main_rv_func_root"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_above="@id/main_play_control">
                        <include
                            android:id="@+id/main_vg_ypage_function_entries_v2"
                            layout="@layout/main_layout_function_entries_custom_y_v2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>

                    </RelativeLayout>

                    <ViewStub
                        android:id="@+id/main_vg_no_copy_right"
                        android:layout="@layout/main_layout_no_copyright_x"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone"
                        android:layout_above="@id/main_play_control"
                        android:layout_marginTop="54dp"/>

                    <!--广告这边不使用viewStub因为防止initUI未走到就有用到此布局的问题-->
                    <RelativeLayout
                        android:id="@+id/main_audio_play_root_lay"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:visibility="invisible"
                        android:layout_below="@id/main_v_status_bar_placeholder"
                        android:layout_above="@+id/main_play_control">

                    </RelativeLayout>

                    <View
                        android:id="@+id/main_v_floating_bg_mask_gradient"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_above="@+id/main_v_floating_progress_mask"
                        android:visibility="invisible"/>

                    <View
                        android:id="@+id/main_v_floating_progress_mask"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_alignParentBottom="true"
                        android:visibility="invisible"/>

                    <ViewStub
                        android:id="@+id/main_vs_floating_progress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginBottom="40dp"
                        android:layout="@layout/main_layout_audio_yplay_page_progress_floating"
                        android:layout_above="@+id/main_play_control"/>

                    <include
                        android:id="@+id/main_play_control"
                        layout="@layout/main_layout_yplay_control"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_gravity="bottom"/>
                </RelativeLayout>

            </com.ximalaya.ting.android.main.playpage.playy.view.XCornerConstraintLayout>
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/main_xplay_tabLayout"
                app:tabGravity="start"
                app:tabMinWidth="1dp"
                android:layout_marginTop="5dp"
                app:tabIndicatorFullWidth="false"
                app:tabIndicatorHeight="11dp"
                app:tabMode="scrollable"
                app:tabPaddingStart="16dp"
                app:tabPaddingEnd="16dp"
                android:background="@null"
                app:tabIndicator="@drawable/main_yplay_tab_indicator"
                app:tabTextAppearance="@style/main_yplay_tabs"
                app:tabSelectedTextColor="@color/main_color_white"
                app:tabTextColor="@color/main_color_white_50"
                app:tabRippleColor="@color/main_transparent"
                app:tabUnboundedRipple="true"
                android:layout_width="match_parent"
                android:layout_height="50dp"/>

        </com.ximalaya.ting.android.main.playpage.playy.view.XCornerAppBarLayout>


        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/main_xplay_tab_container"
            android:elevation="0dp"
            android:layout_width="match_parent"
            app:layout_behavior="com.ximalaya.ting.android.main.playpage.playy.view.appbar.XAppBarLayout$ScrollingViewBehavior"
            android:layout_height="match_parent"/>
    </com.ximalaya.ting.android.main.playpage.playy.view.XPlayLayout>

    <include
        android:id="@+id/main_layout_xpage_top_navigation_bar"
        layout="@layout/main_layout_play_page_navigation_bar_y"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:clipChildren="false"
        android:clipToPadding="false"/>

    <include
        android:id="@+id/main_layout_comment_time_status"
        layout="@layout/main_layout_comment_time_status_dialog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="72dp"
        android:visibility="gone"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/main_xplay_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:elevation="8dp"
        android:orientation="vertical">

        <!--        <include-->
        <!--            android:id="@+id/main_master_entrace"-->
        <!--            layout="@layout/main_layout_x_play_bottom_master_entrance"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="51dp"-->
        <!--            android:visibility="gone"-->
        <!--            tools:visibility="visible" />-->

        <include
            android:id="@+id/main_bottom_vip_bar"
            layout="@layout/main_layout_x_play_bottom_vip_bar"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:visibility="gone"
            tools:visibility="visible"/>

        <FrameLayout
            android:id="@+id/main_layout_xplay_bottom_main_bar"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="@dimen/main_xplay_bottom_height"/>
    </LinearLayout>


    <com.ximalaya.ting.android.main.playpage.playy.view.GoTopView
        android:id="@+id/main_y_play_goto_top"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:visibility="gone"
        android:src="@drawable/main_y_goto_top_ic_top_n_n_line_regular_36"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="100dp"
        android:layout_gravity="bottom|end"
    />


    <View
        android:id="@+id/main_whole_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#33000000"
        android:visibility="gone"/>

    <include
        android:id="@+id/main_xplay_bottom_ad"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        layout="@layout/main_layout_x_play_bottom_ad"/>


    <com.ximalaya.ting.android.main.playpage.view.InterceptParentRelativeLayout
        android:id="@+id/main_rl_input_container"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:clickable="true"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <FrameLayout
        android:id="@+id/main_fl_reader_mode"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:elevation="100dp"
        android:visibility="gone">
    </FrameLayout>

    <!-- 收听进度悬浮组件 -->
    <include
        android:id="@+id/main_listening_progress_floating"
        layout="@layout/main_layout_listening_progress_floating"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|end"
        android:layout_marginTop="100dp"
        android:layout_marginEnd="16dp"
        android:visibility="gone"/>

</com.ximalaya.ting.android.main.playpage.playy.view.ScaleTouchFrame>