package com.ximalaya.ting.android.main.playpage.component;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.ximalaya.ting.android.framework.view.BaseFragment2;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.BaseComponent;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.BaseComponentWithPlayStatusListener;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;

/**
 * 收听进度悬浮组件
 * 显示当前音频的收听进度，支持点击交互
 */
public class ListeningProgressFloatingComponent extends BaseComponentWithPlayStatusListener 
        implements View.OnClickListener {

    private ConstraintLayout mContainer;
    private ProgressBar mProgressBar;
    private ImageView mIconView;
    private TextView mProgressText;
    
    private int mCurrentProgress = 0;
    private int mTotalDuration = 0;
    private boolean mIsVisible = false;
    
    @Override
    public void onCreate(BaseFragment2 fragment) {
        super.onCreate(fragment);
        initViews();
    }
    
    private void initViews() {
        if (mFragment == null) return;
        
        mContainer = mFragment.findViewById(R.id.main_cl_listening_progress_container);
        if (mContainer == null) return;
        
        mProgressBar = mContainer.findViewById(R.id.main_pb_listening_progress);
        mIconView = mContainer.findViewById(R.id.main_iv_listening_progress_icon);
        mProgressText = mContainer.findViewById(R.id.main_tv_listening_progress_text);
        
        mContainer.setOnClickListener(this);
        
        // 初始状态隐藏
        mContainer.setVisibility(View.GONE);
    }
    
    @Override
    public void onResume() {
        super.onResume();
        // 页面恢复时显示组件
        showComponent();
        updateProgress();
    }
    
    @Override
    public void onPause() {
        super.onPause();
        // 页面暂停时隐藏组件
        hideComponent();
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        hideComponent();
    }
    
    private void showComponent() {
        if (mContainer == null || mIsVisible) return;
        
        mIsVisible = true;
        mContainer.setVisibility(View.VISIBLE);
        
        // 添加显示动画
        mContainer.setAlpha(0f);
        mContainer.setScaleX(0.5f);
        mContainer.setScaleY(0.5f);
        
        mContainer.animate()
                .alpha(1f)
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(300)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .start();
    }
    
    private void hideComponent() {
        if (mContainer == null || !mIsVisible) return;
        
        mIsVisible = false;
        mContainer.animate()
                .alpha(0f)
                .scaleX(0.5f)
                .scaleY(0.5f)
                .setDuration(200)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .withEndAction(() -> {
                    if (mContainer != null) {
                        mContainer.setVisibility(View.GONE);
                    }
                })
                .start();
    }
    
    @Override
    public void onPlayStart() {
        super.onPlayStart();
        showComponent();
        updateIconAnimation(true);
    }
    
    @Override
    public void onPlayPause() {
        super.onPlayPause();
        updateIconAnimation(false);
    }
    
    @Override
    public void onPlayStop() {
        super.onPlayStop();
        hideComponent();
        updateIconAnimation(false);
    }
    
    @Override
    public void onPlayProgress(int currPos, int duration) {
        super.onPlayProgress(currPos, duration);
        mCurrentProgress = currPos;
        mTotalDuration = duration;
        updateProgress();
    }
    
    private void updateProgress() {
        if (!canUpdateUi() || mProgressBar == null || mProgressText == null) return;
        
        HandlerManager.postOnUIThread(() -> {
            if (mTotalDuration > 0) {
                int progress = (int) ((float) mCurrentProgress / mTotalDuration * 100);
                mProgressBar.setProgress(progress);
                mProgressText.setText(progress + "%");
            } else {
                mProgressBar.setProgress(0);
                mProgressText.setText("0%");
            }
        });
    }
    
    private void updateIconAnimation(boolean isPlaying) {
        if (mIconView == null) return;
        
        HandlerManager.postOnUIThread(() -> {
            if (isPlaying) {
                // 播放时的脉冲动画
                ObjectAnimator scaleX = ObjectAnimator.ofFloat(mIconView, "scaleX", 1f, 1.1f, 1f);
                ObjectAnimator scaleY = ObjectAnimator.ofFloat(mIconView, "scaleY", 1f, 1.1f, 1f);
                scaleX.setDuration(1000);
                scaleY.setDuration(1000);
                scaleX.setRepeatCount(ObjectAnimator.INFINITE);
                scaleY.setRepeatCount(ObjectAnimator.INFINITE);
                scaleX.start();
                scaleY.start();
            } else {
                // 停止动画
                mIconView.clearAnimation();
                mIconView.setScaleX(1f);
                mIconView.setScaleY(1f);
            }
        });
    }
    
    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.main_cl_listening_progress_container) {
            handleComponentClick();
        }
    }
    
    private void handleComponentClick() {
        Context context = mContext;
        if (context == null) return;
        
        // 获取当前播放信息
        PlayableModel currentSound = XmPlayerManager.getInstance(context).getCurrSound();
        String trackTitle = currentSound != null ? currentSound.getTrackTitle() : "未知音频";
        
        // 计算收听时长
        String listeningTime = formatTime(mCurrentProgress);
        String totalTime = formatTime(mTotalDuration);
        int progressPercent = mTotalDuration > 0 ? (int) ((float) mCurrentProgress / mTotalDuration * 100) : 0;
        
        // 显示提示信息
        String message = String.format("正在收听：%s\n进度：%s / %s (%d%%)", 
                trackTitle, listeningTime, totalTime, progressPercent);
        
        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
        
        // 添加点击反馈动画
        addClickFeedback();
    }
    
    private void addClickFeedback() {
        if (mContainer == null) return;
        
        mContainer.animate()
                .scaleX(0.9f)
                .scaleY(0.9f)
                .setDuration(100)
                .withEndAction(() -> {
                    if (mContainer != null) {
                        mContainer.animate()
                                .scaleX(1f)
                                .scaleY(1f)
                                .setDuration(100)
                                .start();
                    }
                })
                .start();
    }
    
    private String formatTime(int milliseconds) {
        if (milliseconds <= 0) return "00:00";
        
        int seconds = milliseconds / 1000;
        int minutes = seconds / 60;
        seconds = seconds % 60;
        
        return String.format("%02d:%02d", minutes, seconds);
    }
    
    @Override
    public boolean onError(XmPlayerException exception) {
        hideComponent();
        return false;
    }
}
