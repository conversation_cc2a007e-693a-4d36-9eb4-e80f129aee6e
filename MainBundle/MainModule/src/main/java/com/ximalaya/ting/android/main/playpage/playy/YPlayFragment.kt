package com.ximalaya.ting.android.main.playpage.playy

import android.annotation.SuppressLint
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.Gravity
import android.view.HapticFeedbackConstants
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.widget.FrameLayout
import androidx.collection.ArraySet
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.transition.Fade
import androidx.transition.TransitionManager
import androidx.viewpager2.widget.ViewPager2
import com.tencent.bugly.crashreport.CrashReport
import com.ximalaya.ting.android.ad.uve.UveClientManager
import com.ximalaya.ting.android.ad.uve.UveConstants
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.Consumer
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.PadAdaptUtil
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.framework.util.toast.ToastOption
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.constants.CommentConstants
import com.ximalaya.ting.android.host.constants.LoginByConstants
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.play.XPlayPageRef
import com.ximalaya.ting.android.host.fragment.play.data.Theme
import com.ximalaya.ting.android.host.fragment.play.data.XResult
import com.ximalaya.ting.android.host.manager.AudioInfoTraceUtil
import com.ximalaya.ting.android.host.manager.OverseasUserVerifyManager
import com.ximalaya.ting.android.host.manager.PlanTerminateManager
import com.ximalaya.ting.android.host.manager.PlayAdFrequencyMonitor
import com.ximalaya.ting.android.host.manager.TempDataManager
import com.ximalaya.ting.android.host.manager.TrackCollectManager
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage.IFollowAnchorListener
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.appcomment.AppCommentManager
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager
import com.ximalaya.ting.android.host.manager.play.AiSoundTransformManager
import com.ximalaya.ting.android.host.manager.play.PlayPageAbUtil
import com.ximalaya.ting.android.host.manager.play.TrackPlayQualityManager
import com.ximalaya.ting.android.host.manager.play.XStageAreaFreqControlManager
import com.ximalaya.ting.android.host.manager.share.biz.ClaCUserUtils
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage
import com.ximalaya.ting.android.host.manager.track.PlayXRnCommunicationManager
import com.ximalaya.ting.android.host.model.play.CommentModel
import com.ximalaya.ting.android.host.model.play.PlayPageMinorData
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.read.manager.TingTextToReaderManager
import com.ximalaya.ting.android.host.socialModule.IFloatingFragmentDismissListener
import com.ximalaya.ting.android.host.util.CommentHintTool
import com.ximalaya.ting.android.host.util.ConfigAbUtil
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.performance.PageStartOpt
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.other.EmotionSelector
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.play.CommentListAdapterNew
import com.ximalaya.ting.android.main.aduve.UveXPlayFragmentSceneHandler
import com.ximalaya.ting.android.main.aduve.business.UveYellowBarPriorityManagerY
import com.ximalaya.ting.android.main.aduve.business.ad.YUveAdBusiness
import com.ximalaya.ting.android.main.aduve.business.ad.component.YUveAdBannerViewComponentNew
import com.ximalaya.ting.android.main.aduve.business.yellowbar.YUveYellowBarBusiness
import com.ximalaya.ting.android.main.commentModule.fragment.FloatingTrackCommentFragment
import com.ximalaya.ting.android.main.commentModule.fragment.FloatingTrackCommentFragment.Companion.newInstanceForTrack
import com.ximalaya.ting.android.main.commentModule.listener.ITrackCommentBehaviorListener
import com.ximalaya.ting.android.main.commentModule.manager.TrackCommentBehaviorFactory
import com.ximalaya.ting.android.main.commentModule.presenter.CommonCommentPresenter
import com.ximalaya.ting.android.main.commentModule.util.CommentMarkPointUtil
import com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain
import com.ximalaya.ting.android.main.fragment.planterminate.PlanTerminateFragmentNewX
import com.ximalaya.ting.android.main.manager.PlayPageWifiIntranetManager
import com.ximalaya.ting.android.main.manager.TempoManager
import com.ximalaya.ting.android.main.manager.YPlayPageYellowBarManager
import com.ximalaya.ting.android.main.manager.masterClass.MasterClassNoteEventManager
import com.ximalaya.ting.android.main.manager.playPage.PlayListAndHistoryDialogManager
import com.ximalaya.ting.android.main.playModule.dailyNews4.dialog.ReactNativeFloatingDialog
import com.ximalaya.ting.android.main.playModule.masterClass.MasterClassNoteCreateFragment
import com.ximalaya.ting.android.main.playpage.audiointerface.IAudioPlayPageLifecycle
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayUtil
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.interf.ISkinAdShowCallBack
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.remote.IScrollListenerCallBack
import com.ximalaya.ting.android.main.playpage.audioplaypageV2.animate.AnimateBiz
import com.ximalaya.ting.android.main.playpage.audioplaypageV2.animate.AnimationManagerV2
import com.ximalaya.ting.android.main.playpage.audioplaypageV2.animate.AnimationManagerV2.end
import com.ximalaya.ting.android.main.playpage.audioplaypageV2.animate.AnimationManagerV2.reset
import com.ximalaya.ting.android.main.playpage.component.ShareComponentV3
import com.ximalaya.ting.android.main.playpage.dialog.PlanTerminalNewDialog
import com.ximalaya.ting.android.main.playpage.dialog.SleepNoticeDialog
import com.ximalaya.ting.android.main.playpage.dialog.kidvip.KidSleepVipDialogManager
import com.ximalaya.ting.android.main.playpage.dialog.settings.PlaySettingGuideDialog
import com.ximalaya.ting.android.main.playpage.fragment.BasePlayPageTabFragment
import com.ximalaya.ting.android.main.playpage.fragment.PlayFragmentNew
import com.ximalaya.ting.android.main.playpage.internalservice.IAdCoverHideService
import com.ximalaya.ting.android.main.playpage.internalservice.IXAdCoverViewService
import com.ximalaya.ting.android.main.playpage.internalservice.IXPlayFragmentService
import com.ximalaya.ting.android.main.playpage.listener.IPlayCommentFunctionNew
import com.ximalaya.ting.android.main.playpage.listener.IPlayFunctionNew
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager.IOnThemeColorChangedListener
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.manager.PlayPageMinorDataManager
import com.ximalaya.ting.android.main.playpage.manager.commentmanager.PlayCommentManagerNew
import com.ximalaya.ting.android.main.playpage.playx.XPlayViewModel
import com.ximalaya.ting.android.main.playpage.playx.component.danmu.IDanmakuOpenStatusChangeListener
import com.ximalaya.ting.android.main.playpage.playx.view.input.DanmakuInputLayoutX
import com.ximalaya.ting.android.main.playpage.playy.ai.AiGuideTipsComponent
import com.ximalaya.ting.android.main.playpage.playy.ai.PlayAiGuideDialog
import com.ximalaya.ting.android.main.playpage.playy.ai.view.ScaleProxy
import com.ximalaya.ting.android.main.playpage.playy.biz.BizArbiters
import com.ximalaya.ting.android.main.playpage.playy.biz.BizPositionRegister
import com.ximalaya.ting.android.main.playpage.playy.biz.BizResource
import com.ximalaya.ting.android.main.playpage.playy.biz.BizViewResource
import com.ximalaya.ting.android.main.playpage.playy.biz.DynamicSizeFloating
import com.ximalaya.ting.android.main.playpage.playy.biz.IBizArbiter
import com.ximalaya.ting.android.main.playpage.playy.biz.XConfigRegister
import com.ximalaya.ting.android.main.playpage.playy.biz.XYellowBar
import com.ximalaya.ting.android.main.playpage.playy.biz.XYellowBarStatusListener
import com.ximalaya.ting.android.main.playpage.playy.biz.impl.IYellowBarPriorityImpl
import com.ximalaya.ting.android.main.playpage.playy.biz.impl.XDialogManager
import com.ximalaya.ting.android.main.playpage.playy.component.aduve.YUveYellowBarComponent
import com.ximalaya.ting.android.main.playpage.playy.component.aiSound.KidAiSoundDialogXNew
import com.ximalaya.ting.android.main.playpage.playy.component.aiSound.YAiSoundCovertComponent
import com.ximalaya.ting.android.main.playpage.playy.component.base.BaseComponentWithPlayStatusListener
import com.ximalaya.ting.android.main.playpage.playy.component.book.BookComponent
import com.ximalaya.ting.android.main.playpage.playy.component.box.CommonBoxComponent
import com.ximalaya.ting.android.main.playpage.playy.component.box.ICommonBoxClick
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.FunctionComponentCommercialCore
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.XCommercialBroadCastComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.XTrackOverAuditionDialogComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.XTrainingCampComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.XVipBottomBarComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.XVipPrivilegeFloatingComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.Y818TipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.YDolbyAtmosPlayZoneTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.YUniversalCommercialPlayZoneTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.YFreeListenTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.YSamplePlayZoneTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.YSleepEffectTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.YSoundQualityAndEffectTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.YTrackAutoBuyTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.infoViewChild.basic.IInfoViewInteractActionProvider
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.master.XMasterEntranceComponent
import com.ximalaya.ting.android.main.playpage.playy.component.controlbar.XBottomControlBar
import com.ximalaya.ting.android.main.playpage.playy.component.controlbar.YPlayControlBar
import com.ximalaya.ting.android.main.playpage.playy.component.cover.XReaderComponent
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.XPlayAdCoverComponentX
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.polymerizaiton.XPolymerizationComponent
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverComponentsEnum
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverComponentsManager
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YPlayHeight
import com.ximalaya.ting.android.main.playpage.playy.component.freetransfer.YFreeTransferToPaidTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.functionv2.YFunctionEntriesComponentV2
import com.ximalaya.ting.android.main.playpage.playy.component.live.XLiveAndMcEntryComponent
import com.ximalaya.ting.android.main.playpage.playy.component.live.YAnchorLivingTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.navi.YNavigationBarComponent
import com.ximalaya.ting.android.main.playpage.playy.component.podcast.YPodCastTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.podcast.YSeekFloatingCardComponent
import com.ximalaya.ting.android.main.playpage.playy.component.rate.EvaluateAlbumManager
import com.ximalaya.ting.android.main.playpage.playy.component.survey.SurveyUtil
import com.ximalaya.ting.android.main.playpage.playy.component.survey.YSurveyAITimbreTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.survey.YSurveyContentTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.survey.YSurveyDialogFragment.Companion.newInstance
import com.ximalaya.ting.android.main.playpage.playy.component.survey.YSurveyFunctionTipComponent
import com.ximalaya.ting.android.main.playpage.playy.component.tips.ITipsChangeListener
import com.ximalaya.ting.android.main.playpage.playy.component.tips.ITipsShowHideChangeListener
import com.ximalaya.ting.android.main.playpage.playy.component.tips.YBaseTipView
import com.ximalaya.ting.android.main.playpage.playy.component.tips.reward.RewardTipsComponent
import com.ximalaya.ting.android.main.playpage.playy.component.tips.v2.TipPriority
import com.ximalaya.ting.android.main.playpage.playy.component.tips.v2.YTipsComponentV2
import com.ximalaya.ting.android.main.playpage.playy.component.video.IVideoContainer
import com.ximalaya.ting.android.main.playpage.playy.component.video.YVideoComponent
import com.ximalaya.ting.android.main.playpage.playy.dialog.ChooseTrackSoundEffectAiDialogXNew
import com.ximalaya.ting.android.main.playpage.playy.dialog.ChooseTrackSoundEffectAiDialogXNew.TAB_EFFECT
import com.ximalaya.ting.android.main.playpage.playy.dialog.PlayTrackIntroDetailFragment
import com.ximalaya.ting.android.main.playpage.playy.dialog.YPlayListAndHistoryFragment
import com.ximalaya.ting.android.main.playpage.playy.listener.IPlayPageControlChangeListener
import com.ximalaya.ting.android.main.playpage.playy.listener.IPptModeSwitchListener
import com.ximalaya.ting.android.main.playpage.playy.listener.ISkipHeadTailShowListener
import com.ximalaya.ting.android.main.playpage.playy.listener.UnableToPlayStatusAggregator
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayModeSwitcher
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayPageControlManager
import com.ximalaya.ting.android.main.playpage.playy.manager.PodCastDocManager
import com.ximalaya.ting.android.main.playpage.playy.manager.XPlayPageScreenTimeManager
//import com.ximalaya.ting.android.main.playpage.playy.manager.XTipsManager
import com.ximalaya.ting.android.main.playpage.playy.tabs.XPlayPageTab
import com.ximalaya.ting.android.main.playpage.playy.tabs.YTabsComponent
import com.ximalaya.ting.android.main.playpage.playy.tabs.biz.comment.YPlayCommentIndependentFragment
import com.ximalaya.ting.android.main.playpage.playy.tabs.provider.XBottomBarManager
import com.ximalaya.ting.android.main.playpage.playy.tabs.provider.XTabBizProviderFactory
import com.ximalaya.ting.android.main.playpage.playy.tabs.provider.faces.XBottomBarProvider
import com.ximalaya.ting.android.main.playpage.playy.utils.DanmakuUtil
import com.ximalaya.ting.android.main.playpage.playy.utils.PlayUtil
import com.ximalaya.ting.android.main.playpage.playy.view.BottomViewX
import com.ximalaya.ting.android.main.playpage.playy.view.ForwardTouchFrame
import com.ximalaya.ting.android.main.playpage.playy.view.GoTopView
import com.ximalaya.ting.android.main.playpage.playy.view.ScaleTouchFrame
import com.ximalaya.ting.android.main.playpage.playy.view.XAppBarScrollChangeListener
import com.ximalaya.ting.android.main.playpage.playy.view.XCornerAppBarLayout
import com.ximalaya.ting.android.main.playpage.playy.view.XCornerConstraintLayout
import com.ximalaya.ting.android.main.playpage.playy.view.XPlayLayout
import com.ximalaya.ting.android.main.playpage.playy.view.appbar.XAppBarLayout
import com.ximalaya.ting.android.main.playpage.playy.view.appbar.YBehavior
import com.ximalaya.ting.android.main.playpage.playy.xdialog.XDialogArbiter
import com.ximalaya.ting.android.main.playpage.util.PlayCommentUtil
import com.ximalaya.ting.android.main.playpage.util.PlayPageDownloadUtils
import com.ximalaya.ting.android.main.playpage.util.PlayPageFollowUtil
import com.ximalaya.ting.android.main.playpage.util.XPlayCommercialRelatedUtils.checkToShowSHQPrivilegeFloatDialog
import com.ximalaya.ting.android.main.playpage.view.CommentListView
import com.ximalaya.ting.android.main.playpage.view.CommentViewNew
import com.ximalaya.ting.android.main.playpage.playy.component.danmu.XDanmakuComponent
import com.ximalaya.ting.android.main.playpage.playy.component.tips.reward.GiftTipsComponent
import com.ximalaya.ting.android.main.playpage.playy.component.function.YSkipHeadTailPromptComponent
import com.ximalaya.ting.android.main.util.AnchorShareGuideUtil.checkShowMyTrackShareGuide
import com.ximalaya.ting.android.main.view.album.DateUtils
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListener
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListenerExpand
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListenerExtension
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.read.widgets.pageview.TingPageThemeManager
import com.ximalaya.ting.android.util.OsUtil
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmplaysdk.IMediaPlayerControl
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.lang.Math.abs
import java.util.Calendar
import java.util.concurrent.CopyOnWriteArraySet


class YPlayFragment : BasePlayPageTabFragment(), IPlayContainer,
    IPlayPageControlChangeListener, IAdCoverHideService.IAdCoverStateChangeListener,
    IPlayFunctionNew, IDanmakuOpenStatusChangeListener {
    private val TAG = "YPlayFragment"

    private val mPlayerStatusListeners = CopyOnWriteArraySet<IXmPlayerStatusListener>()
    private val mVideoPlayerStatusListeners = CopyOnWriteArraySet<IVideoPlayStatueListener>()
    private val mLifecycleListeners = CopyOnWriteArraySet<IAudioPlayPageLifecycle>()
    private val mStickyChangeListeners = CopyOnWriteArraySet<IStickyChangeListener>()
    private val mAdsStatusListeners = ArraySet<IXmAdsStatusListener>()
    private val mScreenOrientationChanges = CopyOnWriteArraySet<IScreenOrientationChange>()
    private val playTypeChangeListeners = CopyOnWriteArraySet<PlayTypeChangeListener>()
    private val mTopLayoutBgColorListener = mutableSetOf<ITopLayoutBgColorChangeListener>()
    private val mXYellowBarStatusListeners = mutableSetOf<XYellowBarStatusListener>()
    private val mXDanmakuStatusListeners = mutableSetOf<XDanmakuStatusChangeListener>()
    private val mConfigChangeListener = mutableSetOf<IConfigurationChangedListener>()
    private val mTipsShowHideStatusChangeListener = mutableSetOf<ITipsShowHideChangeListener>()
    private val xtabSelectListeners = CopyOnWriteArraySet<XTabChangeCallback>()
    private val mPprModeSwitchChangedListener = mutableSetOf<IPptModeSwitchListener>()

    private var fullScreen = false

    private var bizArbiters: BizArbiters? = null
    private var xConfigRegister: XConfigRegister? = null

    private var xPlayPageScreenTimeManager: XPlayPageScreenTimeManager? = null
//    private var xTipsManager: XTipsManager? = null

    private lateinit var viewModel: XPlayViewModel
    private lateinit var container: XPlayLayout
    private lateinit var topAppbarLayout: XCornerAppBarLayout

    private var controlBarView: ViewGroup? = null
    private var functionBarViewV2: ViewGroup? = null

    private var mBottomCommentBar: BottomViewX? = null
    private var mBottomPlayBar: XBottomControlBar? = null
    private var mBottomBarContainer: View? = null
    private var gotoTopBtn: GoTopView? = null

    private var playContainer: XCornerConstraintLayout? = null
    private var switcher: PlayModeSwitcher? = null

    private var mOnThemeColorChangedListener: IOnThemeColorChangedListener? = null
    private var mForegroundColor = 0
    private var mBackgroundColor = 0
    private var coverComponentsManager: YCoverComponentsManager? = null
    private var mYNavigationBarComponent: YNavigationBarComponent? = null

    private var yTabsComponent: YTabsComponent? = null
    private var mXBottomBarManageComponent: XBottomBarManager? = null
    private var mShareComponent: ShareComponentV3? = null

//    private var mTipsManager: YTipsComponentManager? = null
    public var mControlBar: YPlayControlBar? = null
    private var mUnableToPlayStatusAggregator: UnableToPlayStatusAggregator? = null
    private var videoComponent: YVideoComponent? = null

    private var mLiveAndMcEntryComponent: XLiveAndMcEntryComponent? = null
    private var mCommercialBroadCastComponent: XCommercialBroadCastComponent? = null
    private var mTrackOverAuditionDialogComponent: XTrackOverAuditionDialogComponent? = null
    private var mCommonBoxComponent: CommonBoxComponent? = null
    private var mIsChildProtectMode: Boolean = false

    private var mBookComponent: BookComponent? = null
    private var mReadBookComponent: XReaderComponent? = null
    private var mTipV2Component: YTipsComponentV2? = null

    private var mTopLayoutHeight = 0
    private var mAdService: IAdCoverHideService? = null

    private var isTopStick: Boolean? = null
    private var mLastTrackHasDoc = false // 文稿多场景曝光使用，耦合有点重，没办法
    private var mTitleBarHeight = 0 // 包含标题栏和导航栏的高度
    private var mScrollListenerSet: MutableSet<IScrollListenerCallBack> = mutableSetOf()
    private var mSkipHeadTailShowListenerSet: MutableSet<ISkipHeadTailShowListener> = mutableSetOf()
    private var mCoverChangeListenerSet: MutableSet<CoverChangeListener> = mutableSetOf()
    private var mFunctionEntriesComponentV2: YFunctionEntriesComponentV2? = null

    private var mCurTrackId = 0L

    private var yellowBarPriorityManager: IYellowBarPriorityImpl? = null
    private var mCommentManager: PlayCommentManagerNew? = null
    private var mCommentView: CommentViewNew? = null
    private var mDanmakuIntputBar: DanmakuInputLayoutX? = null
    private var mWholeMaskView: View? = null

    private var mXPolymerizationComponent: XPolymerizationComponent? = null
    private var mSeekFloatingCardComponent: YSeekFloatingCardComponent? = null
    private var mListeningProgressFloatingComponent: com.ximalaya.ting.android.main.playpage.component.ListeningProgressFloatingComponent? = null

    private var isForceExitLandscape = false
    private var mCommentBottomTip: View? = null
    private var mIsAdShowing = false
    private var mIsFirstLoading = true
    private var delayNotifyDataRunnable :Runnable? = null

    private var sleepNoticeDialogJob: Job? = null

    private var mRewardHintDialog: ReactNativeFloatingDialog? = null
    private var mHasClickedClaimReward = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        XPlayPageRef.setXPlayPage(this)
        viewModel = ViewModelProvider(requireActivity()).get(XPlayViewModel::class.java)
        viewModel.isPlayPageY = true

        AnimationManagerV2.reset()
        AnimationManagerV2.initWithConfig(
            PlayPageDataManager.getInstance().curTrackId,
            viewModel.soundInfoLiveData.value?.playPageAbTestResult?.popupArrangementkey
        )
        xPlayPageScreenTimeManager = XPlayPageScreenTimeManager(this)

        (parentFragment as? BaseFragment2)?.setFilterStatusBarSet(true)
        PlayPageWifiIntranetManager.init()
    }

    override fun scope(): CoroutineScope {
        return lifecycleScope
    }

    override fun getPageLogicName() = "新版播放页"

    override fun initUi(savedInstanceState: Bundle?) {
        PlayPageDataManager.getInstance().isAudioPage = true

        topAppbarLayout = findViewById(R.id.main_xplay_top_area)
        YPlayHeight.init(requireActivity(), topAppbarLayout)
        playPageApiForTab?.setTabScrollable(false)
        val scrollChangeListener = object: XAppBarScrollChangeListener() {
            private var lastOffset = 0
            private var reportScrollOver50 = false

            override fun onOffsetChanged(appBarLayout: XAppBarLayout, verticalOffset: Int) {
                super.onOffsetChanged(appBarLayout, verticalOffset)
                Logger.d(TAG, "onScroll $verticalOffset")
                coverComponentsManager?.onPlayContainerScroll(verticalOffset)
                mCommonBoxComponent?.onScrollStateChanged()
                mYNavigationBarComponent?.onPlayContainerScroll(verticalOffset)

                val stick = abs(verticalOffset) >= appBarLayout.totalScrollRange// && !pinComposer.isPinned()
                notifyStickChange(stick)

                if (Math.abs(lastOffset - verticalOffset) > 15) {
                    mXBottomBarManageComponent?.updateShowStatus()
                    lastOffset = verticalOffset
                }

                val asbVerticalOffset = Math.abs(verticalOffset)

                if (asbVerticalOffset < 50) {
                    reportScrollOver50 = false
                }

                if (asbVerticalOffset > 50 && !reportScrollOver50) {
                    reportScrollOver50 = true
                    val soundInfo = viewModel.soundInfoLiveData.value
                    // 新声音播放页-下滑  点击事件
                    XMTraceApi.Trace()
                        .click(66020) // 下滑 50 像素上报
                        .put("currPage", "newPlay")
                        .put("currTrackId", PlayCommentUtil.getCurTrackId(soundInfo).toString())
                        .put("currAlbumId", PlayCommentUtil.getCurAlbumId(soundInfo).toString())
                        .put(XmRequestIdManager.CONT_ID, PlayCommentUtil.getCurTrackId(soundInfo).toString())
                        .put(XmRequestIdManager.CONT_TYPE, "track")
                        .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                        .createTrace()
                }

                playPageApiForTab?.enableOrDisableTopSlideExit(abs(verticalOffset) <= 1
                        && playPageApiForTab?.hasTopFragment() != true
                )
            }

            override fun onScrollStateChanged(state: Int) {
                Logger.d(TAG, "onScroll state = $state")
                notifyScrollListener(state)
            }

        }
        mCommentBottomTip = findViewById(R.id.main_layout_comment_time_status)
        gotoTopBtn = findViewById(R.id.main_y_play_goto_top)
        gotoTopBtn?.setOnClickListener {
            // 新声音播放页-回到顶部  点击事件
            val mSoundInfo = viewModel.soundInfoLiveData.value
            if (mSoundInfo != null) {
                XMTraceApi.Trace()
                    .click(62062) // 用户点击时上报
                    .put("currPage", "newPlay")
                    .put("currTrackId", PlayCommentUtil.getCurTrackId(mSoundInfo).toString())
                    .put("currAlbumId", PlayCommentUtil.getCurAlbumId(mSoundInfo).toString())
                    .createTrace()
            }
            gotoTop(animate = true)
        }

        //main_vg_ypage_tips_container
        controlBarView = findViewById(R.id.main_play_control)
        // functionBarView = findViewById(R.id.main_vg_ypage_function_entries)

        container = findViewById<XPlayLayout?>(R.id.main_play_container).apply {
            topOffset = toolBarHeight()
            xPlayPageStatus = ::getPageStatus
            isShowingToListenGuide = ::isShowingToListenGuide
            nestScrollContentReady = {
                true
//                rnComponent?.nestScrollViewReady()?: false
            }
        }
        topAppbarLayout.addOnOffsetChangedListener(scrollChangeListener)
        yellowBarPriorityManager = UveYellowBarPriorityManagerY()
        playContainer = findViewById(R.id.main_play_top_container)

        val scaleTouchFrame = findViewById<ScaleTouchFrame>(R.id.main_xplay_top_frame)
//        scaleTouchFrame.canScale = {
//            val soundInfo = viewModel.soundInfoLiveData.value
//            YUtils.hasAiEntrance(soundInfo) && playPageApiForTab?.getChildFragmentOnPlayPage { it !is Fragment } == null
//        }
//
//        val scaleProxy = ScaleProxy()
//        scaleProxy.onStart = {
//            if (!PlayAiFragment.hasStart) {
//                PlayAiFragment.hasStart = true
//
//                val soundInfo = viewModel.soundInfoLiveData.value
//                XMTraceApi.Trace()
//                    .click(66153) // 用户点击时上报
//                    .put("currPage", "newPlay")
//                    .put("xmRequestId", XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
//                    .put("action", "gesture") // click 表示点击触发，gesture表示捏和手势触发
//                    .put("contentType", "ai_summary") // 去重使用
//                    .put("contentId", soundInfo?.trackInfo?.trackId.toString()) // 去重使用
//                    .put("currAlbumId", soundInfo?.albumInfo?.albumId?.toString())
//                    .put("currTrackId", soundInfo?.trackInfo?.trackId.toString())
//                    .createTrace()
//                showAiSummary(scaleProxy)
//            }
//            !PlayAiFragment.hasStart
//        }
//        scaleTouchFrame.scaleProxy = scaleProxy.scaleListener

        val xDialogArbiter = XDialogArbiter(
            scaleTouchFrame,
            childFragmentManager
        ).apply { registerScrollListener(this) }
        val bizArbiter = BizArbiters(
            XDialogManager(requireActivity(), childFragmentManager),
            this,
            BizPositionRegister(),
            xDialogArbiter,
        )
        bizArbiters = bizArbiter


        mWholeMaskView = findViewById(R.id.main_whole_mask)

        val forwardTouchFrame: ForwardTouchFrame = findViewById(R.id.main_play_media_pager_wrapper)
        forwardTouchFrame.forwardView = findViewById(R.id.main_background_with_mode)
        forwardTouchFrame.bottomTopView = findViewById(R.id.main_rv_func_root)

        PlayPageControlManager.addPlayControlChangeListener(this)
        initShareComponent()
        initPlayTabs(scrollChangeListener)

        initMainPart()
        initCommercialCore()
        initUnableToPlayAggregator()
        initControlBar()
        initTopNavigationBar()
        initVipPrivilegeFloatingComponent()
        initLiveEntryComponent()
        initBottomBar()
        initCommercialPart()
        initTrackTrainingCampComponent()
        initPodCastTipComponent()
        initSkipHeadTailComponent()
//        initCommentBoxComponent()
//        initDanmakuComponent()
        initDanmuManager()
        initBookComponent()
        initAiSoundCovertComponent()
        initSurveyTipComponent()
        initFreeTransferToPaidTipComponent()
//        initGameComponent()
        initReadBookComponent()
        initLiveTipComponent()
        initListeningProgressFloatingComponent()
//        xTipsManager = XTipsManager(requireActivity(), this, mContainerView!!)
//        addLifecycleListener(xTipsManager!!)
        observer()

        xPlayPageScreenTimeManager?.getAudioPlayPageLifeCycle()?.let { addLifecycleListener(it) }
        AnchorFollowManage.getSingleton().addFollowListener(mFollowAnchorListener)
        LikeTrackManage.addListener(mLikeStateChange)
        TrackCollectManager.getInstance().addListener(mCollectStateChange)
        AlbumEventManage.addListener(mAlbumFavoriteChange)
        TrackCommentBehaviorFactory.newInstance().addManager(mTrackCommentBehaviorListener)
        coverComponentsManager?.setBottomBarShowChangeListener {
//            showOrHideBottomBar(it)
        }
        registerAudioPlayFragmentService()

        mAdService = PlayPageInternalServiceManager.getInstance().getService(IAdCoverHideService::class.java)
        mAdService?.registerAdCoverStateChange(this)
        if (mAdService?.curAdState == 3) {
            end(AnimateBiz.AD)
        }
        initSeekFloatingCardComponent(bizArbiter)
        initRewardTips()
        //trigger init
        ClaCUserUtils.update()
    }

    private fun initRewardTips() {
        RewardTipsComponent(this).also {
            addLifecycleListener(it)
        }

        GiftTipsComponent(this).also {
            addLifecycleListener(it)
        }
    }

    fun getTopOffset(): Int {
        return ((topAppbarLayout.layoutParams as? CoordinatorLayout.LayoutParams)?.behavior as? YBehavior)?.topAndBottomOffset?: 0
    }

    override fun updateBottomBar(bottomBarProvider: XBottomBarProvider) {
        mXBottomBarManageComponent?.updateBottomBar(bottomBarProvider)
    }

    override fun setCustomBottomBar(tabId: Long, bottomBar: BizViewResource?) {
        mXBottomBarManageComponent?.setCustomBottomBar(tabId, bottomBar)
    }

    private fun initPlayTabs(scrollChangeListener: XAppBarScrollChangeListener) {
        val tabBizProviderFactory = XTabBizProviderFactory(this)

        YTabsComponent(this, tabBizProviderFactory).apply {
            setOnScrollChangeListener(scrollChangeListener)
            addLifecycleListener(this)
            onCreate(this@YPlayFragment)
            yTabsComponent = this
        }
        XBottomBarManager(this, findViewById(R.id.main_xplay_tab_container), tabBizProviderFactory).apply {
            onCreate(this@YPlayFragment)
            addLifecycleListener(this)
            mXBottomBarManageComponent = this
        }
    }

//    fun showAiSummary(scaleProxy: ScaleProxy? = null) {
//        val soundInfo = viewModel.soundInfoLiveData.value
//        if (YUtils.hasAiEntrance(soundInfo)) {
//            val themeColor = viewModel.currentTheme.value?.backgroundColor?: Color.BLACK
//            val bitmap = YUtils.getScreenShot(requireView(), themeColor)
//            if (bitmap != null) {
//                view?.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS)
//                val playAiFragment = PlayAiFragment(scaleProxy, soundInfo!!, bitmap, themeColor) { f ->
//                    val argument = Bundle()
//                    argument.putInt(
//                        BundleKeyConstants.KEY_FRAGMENT_OUT_ANIM, com.ximalaya.ting.android.host.R.anim.host_fade_out
//                    )
//                    f.arguments = argument
//                    playPageApiForTab?.removeFragment(f)
//                }
//                playAiFragment.registerSimpleLifecycle(object :
//                    BaseFragment2.SimpleFragmentLifecycleWrapper {
//                    override fun onResume() {
//                        <EMAIL>()
//                    }
//
//                    override fun onPause() {
//                    }
//
//                    override fun onDestroy() {
//                        <EMAIL>()
//                    }
//                })
//                playPageApiForTab?.showFragmentOnPlayPage(
//                    playAiFragment
//                )
//            }
//        }
//    }

    private fun initSeekFloatingCardComponent(bizPositionRegister: BizArbiters) {
        // 可以在点击seekBar的时候再去初始化
        mSeekFloatingCardComponent = YSeekFloatingCardComponent(bizPositionRegister)
        mSeekFloatingCardComponent?.let {
            it.onCreate(this)
            addLifecycleListener(it)
            addPlayStatusListener(it)
        }
    }

    private fun initBookComponent() {
        mBookComponent = BookComponent(coverComponentsManager?.playContainer)
        mBookComponent?.run {
            onCreate(this@YPlayFragment)
            addLifecycleListener(this)
        }
    }

    private var stickyJob: Job? = null

    private fun notifyStickChange(stick: Boolean) {
        if (isTopStick != stick) {
            isTopStick = stick

            stickyJob?.cancel()
            Log.d(TAG, "notifyStickChange: $stick")
            stickyJob = lifecycleScope.async {
                delay(10)
                checkBottomBar()

                isTopStick?.also { lTopStick -> mStickyChangeListeners.forEach { it.onStickyChange(lTopStick) } }

//                mYNavigationBarComponent?.updateStickStatus(stick)
                coverComponentsManager?.notifyStickyChanged(stick)
                if (stick) {
                    if (gotoTopBtn?.visibility != View.VISIBLE) {
                        gotoTopBtn?.visibility = View.VISIBLE
                        // 新声音播放页-回到顶部  控件曝光
                        val mSoundInfo = viewModel.soundInfoLiveData.value
                        if (mSoundInfo != null) {
                            XMTraceApi.Trace()
                                .setMetaId(62063)
                                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                                .put("currPage", "newPlay")
                                .put("currTrackId", PlayCommentUtil.getCurTrackId(mSoundInfo).toString())
                                .put("currAlbumId", PlayCommentUtil.getCurAlbumId(mSoundInfo).toString())
                                .put(XmRequestIdManager.CONT_ID, PlayCommentUtil.getCurTrackId(mSoundInfo).toString())
                                .put(XmRequestIdManager.CONT_TYPE, "track")
                                .put(
                                    XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                                        XmRequestPage.PAGE_PLAY_PAGE))
                                .createTrace()
                        }
                    }
                } else {
                    gotoTopBtn?.visibility = View.GONE
                }
            }
        }
    }

    override fun darkStatusBar(): Boolean {
        if (isTopStick == true) {
            return true
        }
        return super.darkStatusBar()
    }

    private fun clearSticky() {
        isTopStick = null
        stickyJob?.cancel()
    }

    private fun initDanmuManager() {
        mCommentManager = PlayCommentManagerNew(
            CommentConstants.FROM_PAGE_AUDIO,
            this, CommentConstants.BUSINESS_OLD, mWholeMaskView, createTrackIdProvider()
        )
        mCommentView = CommentViewNew(
            mPlayCommentFunctionNew,
            this,
            CommentConstants.FROM_PAGE_AUDIO,
            if (isPageBgDark) CommentListAdapterNew.THEME_DARK else CommentListAdapterNew.THEME_LIGHT
        )
        mCommentView?.setPresenter(CommonCommentPresenter(mCommentView))
        mCommentManager?.setCommentView(mCommentView)
    }

    private fun initSkipHeadTailComponent() {
        val skipHeadTailPromptComponent = YSkipHeadTailPromptComponent.newInstance(this, coverComponentsManager)
        skipHeadTailPromptComponent?.init()
        addLifecycleListener(skipHeadTailPromptComponent!!)
        addAdsStatusListener(skipHeadTailPromptComponent)
    }

    private fun initAiSoundCovertComponent() {
        val aiSoundCovertComponent =
            YAiSoundCovertComponent.newInstance(this, coverComponentsManager)
        aiSoundCovertComponent?.apply {
            init()
            addLifecycleListener(this)
            addAdsStatusListener(this)
        }
    }

    private fun initSurveyTipComponent() {
        YSurveyContentTipComponent.newInstance(this, coverComponentsManager).apply {
            initUI()
            addLifecycleListener(this)
            addPlayStatusListener(this)
        }
        YSurveyAITimbreTipComponent.newInstance(this, coverComponentsManager).apply {
            initUI()
            addLifecycleListener(this)
        }
        YSurveyFunctionTipComponent.newInstance(this, coverComponentsManager).apply {
            initUI()
            addLifecycleListener(this)
        }
    }
    private fun initFreeTransferToPaidTipComponent() {
        YFreeTransferToPaidTipComponent.newInstance(this, coverComponentsManager).apply {
            initUI()
            addLifecycleListener(this)
        }
    }

    private fun initPodCastTipComponent() {
        val podCastDocPopViewManager = PodCastDocManager(this)
        addLifecycleListener(podCastDocPopViewManager)
        addPlayStatusListener(podCastDocPopViewManager)

        val mXPodCastTipComponent = YPodCastTipComponent(podCastDocPopViewManager, this)
        mXPodCastTipComponent.let {
            it.onCreate(this)
            addLifecycleListener(it)
            addPlayStatusListener(it)
        }
    }

    private fun initCommentBoxComponent() {
        mCommonBoxComponent = CommonBoxComponent.newInstance(this, R.id.main_layout_comment_box_stub)
        mCommonBoxComponent?.let {
            mCommonBoxComponent?.setBoxClickListener(mCommentBoxClick)
            coverComponentsManager?.let {
                mCommonBoxComponent?.setCoverComponentsManagerV3(it)
            }
            addLifecycleListener(it)
            addPlayStatusListener(it)
        }
    }

    override fun getImmersiveComponent(): com.ximalaya.ting.android.main.playpage.playy.component.immersive.XImmersive? {
        return coverComponentsManager?.immersive
    }


    override fun onDocSwitchChange(docShow: Boolean) {
        coverComponentsManager?.onDocSwitchChanged(docShow)
    }

    override fun getCurTrack(): Track? = viewModel.soundInfoLiveData.value?.trackInfo2TrackM()

    override fun getCurTrackId(): Long {
        return mCurTrackId
    }

    override fun getFragment(): BaseFragment2 = this

    override fun changeTopContainBackgroundColor(mainColorId: Int, subColorId: Int) {
//        playContainer?.
//        setDarkMode(mainColorId != 0)
//        ViewStatusUtil.setVisible(if (mainColorId != 0) View.INVISIBLE else View.VISIBLE, stageShadeView)
        notifyTopLayoutBgColorChangeListener(mainColorId, subColorId)
    }

    override fun addTopLayoutBgColorChangeListener(listener: ITopLayoutBgColorChangeListener) {
        mTopLayoutBgColorListener?.add(listener)
    }

    override fun removeTopLayoutBgColorChangeListener(listener: ITopLayoutBgColorChangeListener) {
        mTopLayoutBgColorListener?.remove(listener)
    }

    private fun notifyTopLayoutBgColorChangeListener(mainColorId: Int, subColorId: Int) {
        mTopLayoutBgColorListener?.forEach {
            it.onChangeTopLayoutBgColor(mainColorId, subColorId)
        }
    }

    override fun addXYellowBarStatusListener(listener: XYellowBarStatusListener) {
        mXYellowBarStatusListeners.add(listener)
    }

    override fun removeXYellowBarStatusListener(listener: XYellowBarStatusListener) {
        mXYellowBarStatusListeners.remove(listener)
    }

    override fun onDanmakuEntryBtClick() {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mContext)
            return
        }
        if (mDanmakuIntputBar == null) {
            initDanmakuInput()
        }

        val soundInfo = viewModel.soundInfoLiveData.value
        if (soundInfo?.otherInfo != null && soundInfo?.otherInfo?.disallowBarrageForUGCRisk() == true) {
            val message =
                if (!TextUtils.isEmpty(soundInfo?.otherInfo?.allowBarrageTypeDesc)) soundInfo.otherInfo?.allowBarrageTypeDesc else "该声音当前不允许发送弹幕"
            CustomToast.showFailToast(message)
        } else {
            val fullMode = if (YUtils.isLandScape(context)) {
                "landscape"
            } else {
                if (isFullScreen()) {
                    "full"
                } else {
                    "half"
                }
            }
            var trackFrom = if (PlayUtil.isPptPage()) "ppt" else if (PlayUtil.isVideoPage()) "video" else "track"

            mDanmakuIntputBar?.setFullScreenMode(fullMode, trackFrom)
            // mDanmakuIntputBar?.showHideSendViewWhenLandscape(resourcesSafe.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE)
            val soundInfo = viewModel.soundInfoLiveData.value
            val curTrack = soundInfo?.trackInfo2TrackM()
            if (curTrack != null && (!curTrack.isPaid || curTrack.isFree || curTrack.isAuthorized)) {
                mCommentManager?.setSinglePlayMode(CommentConstants.COMMENT_TYPE_SEND_DANMUKU)
            }
            mCommentManager?.setInputHint("快来发条弹幕吧")
//            mCommentManager!!.parentCommentId = parentCommentId
            mCommentManager?.toggleInputBar(
                CommentConstants.COMMENT_TYPE_SEND_DANMUKU, "", PlayCommentUtil.isAuthorSendComment(soundInfo),
                true, true
            )
            mCommentManager?.setInputContent("")
        }
    }

    override fun addXDanmakuStatusChangeListener(listener: XDanmakuStatusChangeListener) {
        mXDanmakuStatusListeners.add(listener)
    }

    override fun removeXDanmakuStatusChangeListener(listener: XDanmakuStatusChangeListener) {
        mXDanmakuStatusListeners.remove(listener)
    }

    private fun initDanmakuComponent() {
        XDanmakuComponent.newInstance(coverComponentsManager, this).apply {
            this.initUi(mContainerView)
            addPlayStatusListener(this)
            addLifecycleListener(this)
        }
    }

    private fun initDanmakuInput() {
        if (mDanmakuIntputBar != null || mContext == null) {
            return
        }
        mDanmakuIntputBar = DanmakuInputLayoutX(mContext, viewModel.soundInfoLiveData.value)
        mDanmakuIntputBar?.setIDanmakuOpenStatusChangeListener(this)
        mDanmakuIntputBar?.let {
            addLifecycleListener(it)
        }

        if (view is ViewGroup) {
            val params =
                FrameLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
            params.gravity = Gravity.BOTTOM
            (view as ViewGroup).addView(mDanmakuIntputBar, params)
        }
        mCommentManager?.setCommentQuoraInputLayout(mDanmakuIntputBar)
        mDanmakuIntputBar?.visibility = View.GONE
        // 收起键盘时同时隐藏输入面板
        mDanmakuIntputBar?.setKeyboardListener(object : EmotionSelector.IKeyboardListener2 {
            override fun toggle(show: Boolean) {
                if (!show) {
                    mCommentManager?.hideCommentQuoraInputLayout()
                }
            }

            override fun toggle(show: Boolean, requestEmotion: Boolean) {
                if (!show && !requestEmotion) {
                    mCommentManager?.hideCommentQuoraInputLayout()
                }
            }
        })
    }

    private fun notifyXYellowBarStatusListener(yellowBar: XYellowBar, isShow: Boolean) {
        mXYellowBarStatusListeners.forEach {
            if (isShow) {
                it.onShow(yellowBar)
            } else {
                it.onHide(yellowBar)
            }
        }
    }

    private val mCommentBoxClick: ICommonBoxClick = object : ICommonBoxClick {
        override fun onCommentBoxClick(commentId: Long) {
//            openFloatingCommentPanel(
//                needInputTimeMark = isAudioPage(),
//                enableAutoShowKeyboard = false,
//                anchorCommentId = commentId
//            )
        }
    }

    private fun initLiveEntryComponent() {
        mLiveAndMcEntryComponent = XLiveAndMcEntryComponent(this, coverComponentsManager, this)
    }

    private fun initTrackOverAuditionDialogComponent() {
        mTrackOverAuditionDialogComponent = XTrackOverAuditionDialogComponent()
        mTrackOverAuditionDialogComponent?.let {
            it.onCreate(this)
            addLifecycleListener(it)
        }
    }

    private fun initUveClientManager() {
        YPlayPageYellowBarManager.instance.init(mContext)
        initUveYellowBarBusinessModel()
        initUveAdBusinessModel()
        var uveSceneHandler = UveXPlayFragmentSceneHandler()
        uveSceneHandler?.let {
            addLifecycleListener(it)
            addPlayStatusListener(it)
            addAdsStatusListener(it)
        }
    }

    private fun initUveAdBusinessModel() {
       val mUveBannerViewComponent = YUveAdBannerViewComponentNew()
        mUveBannerViewComponent.let {
            if (it is BaseComponentWithPlayStatusListener) {
                it.onCreate(this)
                addLifecycleListener(it)
                addPlayStatusListener(it)
            }
            addAdsStatusListener(it)
            yellowBarPriorityManager?.registerPriorityListener(XYellowBar.AD, it)
        }
        var mUveAdBusiness: YUveAdBusiness = YUveAdBusiness.getInstanceBusiness()
        mUveAdBusiness.addPositionAdComponent(
            UveConstants.Position.BANNER.positionID,
            mUveBannerViewComponent
        )
        UveClientManager.getInstance().registerBusiness(mUveAdBusiness)
    }

    //商业化小黄条通过 UVE 管理
    private fun initUveYellowBarBusinessModel() {

        var mUveYellowBarViewComponent =
            YUveYellowBarComponent(YPlayPageYellowBarManager.instance.getCommercialContainer(mContext))
        mUveYellowBarViewComponent?.let {
            it.onCreate(this)
            addLifecycleListener(it)
            addPlayStatusListener(it)
            yellowBarPriorityManager?.registerPriorityListener(XYellowBar.COMMERCIAL, it)
        }

        var uveBusiness: YUveYellowBarBusiness = YUveYellowBarBusiness.getInstanceBusiness()
        uveBusiness.addPositionAdComponent(
            UveConstants.Position.BANNER.positionID,
            mUveYellowBarViewComponent
        )

        UveClientManager.getInstance().registerBusiness(uveBusiness)
    }

    private fun initCommercialBroadCastComponent() {
        mCommercialBroadCastComponent = XCommercialBroadCastComponent()
        mCommercialBroadCastComponent?.let {
            it.bcCallBack = object : XCommercialBroadCastComponent.IXPlayFragmentBcCallBack {
                override fun callReloadSoundInfo(isForce: Boolean) {
                    // reload
                    loadData(isForce)
                }

                override fun callSetTrackAuthorityChanged(isTrackAuthorityChanged: Boolean) {
                    setTrackAuthorityChanged(isTrackAuthorityChanged)
                }
            }
            it.onCreate(this)
            addLifecycleListener(it)
            addPlayStatusListener(it)
        }
    }

    private fun initTrackTrainingCampComponent() {
        val trainingCampComponent = XTrainingCampComponent.newInstance()
        trainingCampComponent.let {
            it.onCreate(this)
            addLifecycleListener(it)
            addPlayStatusListener(it)
        }
    }

    override fun onTrackAuthorityChangedWhileResume() {
        loadData(true)
    }

    private fun checkIsPptOrVideoLandscape(soundInfo: PlayingSoundInfo?) {
        soundInfo?.let {
            if (YUtils.isLandScape(context)) {
                val trackInfo = it.trackInfo
                if ((soundInfo.trackInfo?.isRichAudio == false && !isVideoMode())
                    || (trackInfo?.isVideo == false && isVideoMode())) {
                    //不是ppt且不是视频
                    exitLandScape()
                }
            }
        }
    }

    private fun observer() {
        viewModel.currentTheme.observe(this, Observer {
            val backgroundColor = it?.backgroundColor?: return@Observer
            TingPageThemeManager.cachePlayPageColor(
                PlayPageDataManager.getInstance().soundInfo,
                backgroundColor
            )
            gotoTopBtn?.updateThemeColor(backgroundColor)
        })
        viewModel.soundInfoLiveData.observe(this, Observer {
            checkFunctionVersion()
            yellowBarPriorityManager?.onSoundInfoLoaded(it)
            it?.let { soundInfo ->
                it.trackInfo?.albumId?.let { albumId ->
                    TempoManager.getInstance().readCurrentAlbumTempo(albumId)
                }
                //更新音视频状态
//                checkVideoStatus()
                //更新动效管理配置
                AnimationManagerV2.initWithConfig(
                    PlayPageDataManager.getInstance().curTrackId,
                    soundInfo.playPageAbTestResult?.popupArrangementkey
                )

                checkShowMyTrackShareGuide(this, soundInfo)

                val runnable = Runnable {
                    showLiveAndMcEntry(soundInfo)

                    mShareComponent?.setSoundInfo(soundInfo)
                    mLifecycleListeners.forEach { lifeCycle -> lifeCycle.onSoundInfoLoaded(soundInfo) }

                    mCommercialCore?.checkToShowCommercialView(soundInfo, null, true)
                }

                Logger.d("PlayPageOpt_", "mIsFirstLoading: $mIsFirstLoading")

                if (mIsFirstLoading && PageStartOpt.PlayPageOpt.enable()) {
                    HandlerManager.removeCallbacks(delayNotifyDataRunnable)
                    delayNotifyDataRunnable = runnable
                    HandlerManager.postOnUIThreadDelay(runnable, 100)
                } else {
                    runnable.run()
                }


                // 会员播放声音情况下判断是否请求
                if (it.albumInfo?.getVipFreeType() == 1) {
                    OverseasUserVerifyManager.checkToRequestPush()
                }

                showSleepNoticeDialog(soundInfo)

                checkShowAwardHintDialog()

                mIsFirstLoading = false;
            }
            checkIsPptOrVideoLandscape(it)
        })

        viewModel.noTab.observe(this) {

        }

        viewModel.minorLiveData.observe(this, Observer {
            it?.let { minorData ->
                if (viewModel.fromVideoChannelLiveData.value != true) {
                    mControlBar?.onMinorDataLoaded(minorData)
                }
            }
            mShareComponent?.setMinorDate(it)
        })
    }

    private fun showSleepNoticeDialog(soundInfo: PlayingSoundInfo) {
        if (!PlayPageAbUtil.debugSleepNotice() && (
                    soundInfo.whiteListInfo?.timedClockUser != true ||
                            (soundInfo.albumInfo?.realTimePlayDuration?:0) < 5 * 60)) {
            return
        }

        if (PlayPageAbUtil.usingMusicAlarm()) {
            sleepNoticeDialogJob?.cancel()
            if (mIsFirstLoading) {
                sleepNoticeDialogJob = lifecycleScope.launch {
                    delay(5000)
                    SleepNoticeDialog.tryShow(this@YPlayFragment, soundInfo)
                }
            } else {
                SleepNoticeDialog.tryShow(this@YPlayFragment, soundInfo)
            }
        }
    }

    private fun checkFunctionVersion() {

    }

    override fun getFunctionEntriesComponent(): YFunctionEntriesComponentV2? {
        return mFunctionEntriesComponentV2
    }

    override fun isVideoUserInputEnabled(): Boolean {
        return false
    }

    fun isFromPlayBar(): Boolean = playPageApiForTab?.isFromPlayBar ?: false

    private fun initTopNavigationBar() {
        mYNavigationBarComponent = YNavigationBarComponent.newInstance(this, requireView(), this)
        addLifecycleListener(mYNavigationBarComponent!!)
        coverComponentsManager?.setNavigationBarComponent(mYNavigationBarComponent)
    }

    private fun initVipPrivilegeFloatingComponent() {
        val vipPrivilegeFloatingComponent = XVipPrivilegeFloatingComponent()
        vipPrivilegeFloatingComponent.onCreate(this)
        addLifecycleListener(vipPrivilegeFloatingComponent)
        addPlayStatusListener(vipPrivilegeFloatingComponent)
        addAdsStatusListener(vipPrivilegeFloatingComponent)
    }

    private fun initListeningProgressFloatingComponent() {
        mListeningProgressFloatingComponent = com.ximalaya.ting.android.main.playpage.component.ListeningProgressFloatingComponent()
        mListeningProgressFloatingComponent?.let {
            it.onCreate(this)
            addLifecycleListener(it)
            addPlayStatusListener(it)
        }
    }

    private fun initControlBar() {
        val controlBar = YPlayControlBar(this)
        mControlBar = controlBar

        controlBar.onCreate(this)
        controlBar.init(view as ViewGroup)
        mPlayerStatusListeners.add(controlBar)
        addVideoPlayStatusListener(controlBar)
        addLifecycleListener(controlBar)
        mAdsStatusListeners.add(controlBar)
        coverComponentsManager?.addCoverChangeListener(controlBar)
    }

    override fun enterLandScape() {
        if (activity != null && activity is BaseFragmentActivity) {
            (activity as BaseFragmentActivity).mIsFullScreen = true
        }
        (parentFragment as? PlayFragmentNew)?.interceptTopSlideExit(true)
        YUtils.enterLandScape(mActivity)
        gotoTop()
        mScreenOrientationChanges.forEach { it.onScreenOrientationChange(true) }
        mConfigChangeListener.forEach { it.onConfigurationChangedByUser(true) }
        ensureScapeWidgets(true)
        videoComponent?.onConfigurationChangedByUser(true)
    }

    @SuppressLint("SourceLockedOrientationActivity")
    override fun exitLandScape() {
        (parentFragment as? PlayFragmentNew)?.interceptTopSlideExit(false)
        YUtils.exitLandScape(mActivity, isForceExitLandscape)
        mScreenOrientationChanges.forEach { it.onScreenOrientationChange(false) }
        mConfigChangeListener.forEach { it.onConfigurationChangedByUser(false) }
        ensureScapeWidgets(false)
        videoComponent?.onConfigurationChangedByUser(false)
    }

    override fun onBackEvent(): Boolean {
        if (mActivity?.getRequestedOrientation() == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) {
            exitLandScape()
            return true
        }
        if (coverComponentsManager?.currentMutexCoverComponent is XPlayAdCoverComponentX) {
            Log.d(TAG, "onBackEvent;  当前展示组件是 广告组件")
            (coverComponentsManager?.currentMutexCoverComponent as XPlayAdCoverComponentX).onBackEvent()
        }
        PlayPageLargeAdProvider.isClosePlayPage = true
        return false
    }

    override fun addIConfigurationChangedListener(listener: IConfigurationChangedListener) {
        mConfigChangeListener.add(listener)
    }

    override fun removeIConfigurationChangedListener(listener: IConfigurationChangedListener) {
        mConfigChangeListener.remove(listener)
    }

    override fun hideControlBarWhenLandscape() {
        mYNavigationBarComponent?.show(false)
        //hide function bar
        mFunctionEntriesComponentV2?.xhide()
        mControlBar?.hideControlBarView()
    }

    fun hideFloatingView() {
        mControlBar?.hideFloatingProgress()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        videoComponent?.onConfigurationChanged(newConfig)
        coverComponentsManager?.onConfigurationChanged(newConfig)
    }

    private fun ensureScapeWidgets(isLandScape: Boolean) {
        if (isLandScape) {
            hideControlBarWhenLandscape()
        } else {
            mYNavigationBarComponent?.show(true)
            mFunctionEntriesComponentV2?.xshow()
            mControlBar?.showControlBarView()
        }
    }

    override fun onVideoStart(videoSourceUrl: String?) {
        mVideoPlayerStatusListeners.forEach { it.onVideoStart(videoSourceUrl) }
    }

    override fun onVideoPause(videoSourceUrl: String?, playedTime: Long, duration: Long) {
        mVideoPlayerStatusListeners.forEach { it.onVideoPause(videoSourceUrl, playedTime, duration) }
    }

    override fun onVideoStop(videoSourceUrl: String?, playedTime: Long, duration: Long) {
        mVideoPlayerStatusListeners.forEach { it.onVideoStop(videoSourceUrl, playedTime, duration) }
    }

    override fun onVideoProgress(videoSourceUrl: String?, curPosition: Long, duration: Long) {
        mVideoPlayerStatusListeners?.forEach { it.onVideoProgress(videoSourceUrl,curPosition,duration) }
    }

    override fun setVideoPlayView(view: IMediaPlayerControl?) {
        mVideoPlayerStatusListeners.forEach { it.setVideoPlayView(view) }

    }

    override fun toggleFullscreen(traceItem: String) {
    }

    override fun showPlayList() {
        if (isVideoMode()) {
            videoComponent?.showPlayList()
        } else {
            showPlayList(YPlayListAndHistoryFragment.newInstance().apply {
                setDismissCallback {
                    mControlBar?.updateNextAndPreBtnStatus()
                }
            })
        }
    }

    override fun isVideoPlaying(): Boolean {
        if (isVideoMode()) {
            return videoComponent?.isPlaying() == true
        }
        return false
    }

    override fun playNext() {
        if (isVideoMode()) {
            videoComponent?.playNext()
        } else {
            AudioPlayUtil.playNext(context)
        }
    }

    override fun playPause() {
        if (isVideoMode()) {
            videoComponent?.playOrPause()
        } else {
            AudioPlayUtil.playOrPause(this, context, viewModel.soundInfoLiveData.value)
        }
    }

    override fun isPlaying(): Boolean {
        return if (isVideoMode()) {
            videoComponent?.isPlaying() ?: false
        } else {
            XmPlayerManager.getInstance(context).isPlaying
        }
    }

    override fun showPlayList(playListFragment: YPlayListAndHistoryFragment) {
        mControlBar?.showControlBarView()

        val targetType = DynamicSizeFloating.PLAY_LIST
        playListFragment.setCloseCallback { bizArbiters?.dismiss(targetType) }
        bizArbiters?.showFloating(targetType, object : BizResource {
            override fun provideView() = null
            override fun provideFragment() = playListFragment
            override fun onShow() {}
            override fun onHide() {}
        }, byClick = true)
    }

    override fun getPageStatus(): XPlayPageStatus {
        return XPlayPageStatus(
            isFullScreen = fullScreen,
            isAudioMode = isAudioPage(),
            isSticky = isTopStick == true,
            isLandScape = YUtils.isLandScape(context),
        )
    }

    private fun toolBarHeight(): Int {
        if (viewModel.noTab.value == true) {
            return 44.dp + BaseUtil.getStatusBarHeight(mContext)
        }
        return 34.dp + BaseUtil.getStatusBarHeight(mContext)
    }

    override fun getBizArbiters(): IBizArbiter? {
        return bizArbiters
    }

    override fun getXConfigRegister(): XConfigRegister {
        if (xConfigRegister == null) {
            xConfigRegister = XConfigRegister()
        }
        return xConfigRegister!!
    }


    override fun cancelCoverAreaScreenOff() {
        xPlayPageScreenTimeManager?.cancelScreenOffTask()
    }

    override fun showTips(id: Int, view: View): Boolean? {
        if (view is YBaseTipView || id == TipPriority.AI_GUIDE.id) {
            return mTipV2Component?.showTips(id, view)
        }
        return false
    }

    fun addAiGuideView(view: View) {
        mTipV2Component?.addAiGuideView(view)
    }

    override fun dismissTips(id: Int) {
        mTipV2Component?.dismissTips(id)
    }

    override fun addTipsChangeListener(listener: ITipsChangeListener) {
        // mTipsChangeListener?.add(listener)
    }

    override fun removeTipsChangeListener(listener: ITipsChangeListener) {
        // mTipsChangeListener?.remove(listener)
    }

    override fun addTipsShowHideStatusChangeListener(listener: ITipsShowHideChangeListener) {
        mTipsShowHideStatusChangeListener?.add(listener)
    }

    override fun removeTipsShowHideStatusChangeListener(listener: ITipsShowHideChangeListener) {
        mTipsShowHideStatusChangeListener?.remove(listener)
    }

    override fun onTipsShowHideChange(id: Int, isShow: Boolean) {
        mTipsShowHideStatusChangeListener?.forEach { it.onTipsShowChange(id, isShow) }
    }

    override fun onLiveMcEntryExpandAnimatorChange(isStart: Boolean) {
        mYNavigationBarComponent?.onLiveMcEntryExpandAnimatorChange(isStart)
    }

    private fun initUnableToPlayAggregator() {
        mUnableToPlayStatusAggregator = UnableToPlayStatusAggregator().apply {
            onCreate(this@YPlayFragment)
            addLifecycleListener(this)
            addPlayStatusListener(this)
            coverComponentsManager?.addCoverChangeListener(this)
        }
    }

    private fun initBottomBar() {
        mBottomBarContainer = findViewById(R.id.main_xplay_bottom_bar)
        XMasterEntranceComponent(this).apply {
            onCreate(this@YPlayFragment)
            registerBroadcastListener()
            addPlayStatusListener(this)
            addLifecycleListener(this)
        }
        XVipBottomBarComponent().apply {
            onCreate(this@YPlayFragment)
            setYellowBarUseUVESwitch(true)
            initUi(requireView())
            addPlayStatusListener(this)
            addLifecycleListener(this)
            addStickyChangeListener(this)
        }
    }

    private fun hideBottomBar() {
        mBottomBarContainer?.visibility = View.GONE
    }

    private fun checkBottomBar() {
        if (!fullScreen) {
            if (mBottomBarContainer?.visibility != View.VISIBLE) {
                mBottomBarContainer?.visibility = View.VISIBLE
            }
        } else {
            if (mBottomBarContainer?.visibility != View.GONE) {
                mBottomBarContainer?.visibility = View.GONE
            }
        }
    }

//    private fun initTipsContainerComponent() {
//        mAudioTipContainerComponent = YAudioTipsContainerComponent(this).apply {
//            onCreate(this@YPlayFragment)
//            init(requireView())
//            addLifecycleListener(this)
//        }
//    }

    private fun initFunctionComponentV2(coverComponentsManager: YCoverComponentsManager) {
        functionBarViewV2 = findViewById(R.id.main_vg_ypage_function_entries_v2)
        if (mFunctionEntriesComponentV2 == null) {
            mFunctionEntriesComponentV2 =
                YFunctionEntriesComponentV2.newInstance(coverComponentsManager, this)
            mFunctionEntriesComponentV2?.init(requireView())
            mFunctionEntriesComponentV2?.let {
                addLifecycleListener(it)
                addPlayTypeChangeListener(it)
            }
            this.coverComponentsManager?.addCoverChangeListener(mFunctionEntriesComponentV2)
            this.coverComponentsManager?.setFunctionEntriesComponentV2(mFunctionEntriesComponentV2)
        }
    }

    private var vTitleBarPlaceHolder: View? = null
    private fun initMainPart() {
        vTitleBarPlaceHolder = findViewById(R.id.main_v_title_bar_placeholder)
        vTitleBarPlaceHolder?.layoutParams?.height = BaseUtil.getStatusBarHeight(mContext) + 50.dp
        val vStatusBarPlaceHolder: View = findViewById(R.id.main_v_status_bar_placeholder)
        vStatusBarPlaceHolder.layoutParams.height = BaseUtil.getStatusBarHeight(mContext)
        mTitleBarHeight = vStatusBarPlaceHolder.layoutParams.height
        initCoverArea()
        initDanmakuComponent()
//        initTipsManager()
        initFunctionComponentV2(coverComponentsManager!!)
//        initTipsContainerComponent()
        initTipV2Component()
    }

    private fun initTipV2Component() {
        mTipV2Component = YTipsComponentV2(this, lifecycleScope).apply {
            onCreate(this@YPlayFragment)
            coverComponentsManager?.setTipsComponentV2(this)
            init(requireView())
            addPlayStatusListener(this)
        }

        //

        AiGuideTipsComponent(this).apply {
            addLifecycleListener(this)
        }
    }

    // // 商业化相关内容
    private var mCommercialCore: FunctionComponentCommercialCore? = null


    private fun initCommercialCore() {
        val debugMode = ToolUtil.getDebugSystemProperty("debug.commercial_tips.combine", "0") == "1"
        if (debugMode || !ConfigureCenter.getInstance()
                .getBool(CConstants.Group_android.GROUP_NAME, "item_commercial_tips_combine", false)
        ) {
            initTrackAutoBuyTipComponent()
            initSleepAidTipComponent()
            initYSoundQualityAndEffectTipComponent()
            initFreeListenTipComponent()
//            init818TipComponent()
            initSamplePlayZoneTipComponent()
            initDolbyAtmospherePlayZoneTipComponent()
            initUniversalCommercialPlayZoneTipComponent()
        } else {
            mCommercialCore = FunctionComponentCommercialCore(object :
                com.ximalaya.ting.android.main.playpage.playy.component.commercial.infoViewChild.basic.IInfoViewDataProvider {
                override fun getCurrentTrackIdFromComponent(): kotlin.Long {
                    return viewModel.trackId
                }

                override fun getCurrentAlbumIdFromComponent(): kotlin.Long {
                    return com.ximalaya.ting.android.main.playpage.util.PlayCommentUtil.getCurAlbumId(viewModel.soundInfoLiveData.value)
                }

                override fun getPlayingSoundInfoFromComponent(): com.ximalaya.ting.android.host.model.play.PlayingSoundInfo? {
                    return viewModel.soundInfoLiveData.value
                }

                override fun getCurrentTrackFromComponent(): com.ximalaya.ting.android.opensdk.model.track.Track? {
                    return viewModel.soundInfoLiveData.value?.trackInfo2TrackM()
                }

                override fun getContextFromComponent(): android.content.Context? {
                    return context
                }

                override fun getFragmentFromComponent(): com.ximalaya.ting.android.host.fragment.BaseFragment2? {
                    return this@YPlayFragment
                }

                override fun getFragmentManagerFromComponent(): androidx.fragment.app.FragmentManager? {
                    return fragmentManager
                }
            }, object : IInfoViewInteractActionProvider {})
            mCommercialCore?.init(this)
            coverComponentsManager?.addCoverChangeListener {
                if (it == YCoverComponentsEnum.AD_COMPONENT || it == YCoverComponentsEnum.FRIEND_ONLY_COMPONENT) {
                    mIsAdShowing = true
                    if (isGradeSAdShow()) {
                        mCommercialCore?.hideCommercialViewForce()
                    }
                } else {
                    mIsAdShowing = false
                    mCommercialCore?.showCommercialView()
                }
            }
        }
    }

    private fun initTrackAutoBuyTipComponent(){
        YTrackAutoBuyTipComponent.newInstance(this, coverComponentsManager).apply {
            initUI()
            addPlayStatusListener(this)
            addLifecycleListener(this)
        }
    }

    private fun initSleepAidTipComponent(){
        YSleepEffectTipComponent.newInstance(this, coverComponentsManager).apply {
            addPlayStatusListener(this)
            addLifecycleListener(this)
        }
    }

    private fun initSamplePlayZoneTipComponent() {
        YSamplePlayZoneTipComponent.newInstance(this, coverComponentsManager).apply {
            addPlayStatusListener(this)
            addLifecycleListener(this)
        }
    }

    private fun initUniversalCommercialPlayZoneTipComponent() {
        YUniversalCommercialPlayZoneTipComponent.newInstance(this, coverComponentsManager).apply {
            addPlayStatusListener(this)
            addLifecycleListener(this)
        }
    }

    private fun initDolbyAtmospherePlayZoneTipComponent() {
        YDolbyAtmosPlayZoneTipComponent.newInstance(this, coverComponentsManager).apply {
            addPlayStatusListener(this)
            addLifecycleListener(this)
        }
    }

    private fun init818TipComponent(){
        Y818TipComponent.newInstance(this, coverComponentsManager).apply {
            initUI()
            addPlayStatusListener(this)
            addLifecycleListener(this)
        }
    }

    private fun initFreeListenTipComponent(){
        YFreeListenTipComponent.newInstance(this, coverComponentsManager).apply {
            initUI()
            addPlayStatusListener(this)
            addLifecycleListener(this)
        }
    }

    private fun initYSoundQualityAndEffectTipComponent(){
        YSoundQualityAndEffectTipComponent.newInstance(this, coverComponentsManager).apply {
            initUI()
            addPlayStatusListener(this)
            addLifecycleListener(this)
        }
    }
    private fun isGradeSAdShow() : Boolean{
        var isShowing = false
        PlayPageInternalServiceManager.getInstance()
            .getService(IXAdCoverViewService::class.java)?.let {
                isShowing = it.adComponentIsShowing() && it.curAdIsGradeS()
            }
        return isShowing
    }

    override fun getTitleBarPlaceHolder(): View? {
        return vTitleBarPlaceHolder
    }

    override fun getTipsContaineriView(): View? {
        return mTipV2Component?.tipsViewHolderV
    }

    override fun forceShowOrHideTips(show: Boolean) {
        mTipV2Component?.forceShowOrHideTips(show)
//        mTipsManager?.forceShowOrHideTips(show)
//        mAudioTipContainerComponent?.forceShowOrHideTips(show)
    }

    override fun getCurShowTipConfig(): TipPriority? = mTipV2Component?.getCurShowTipConfig()
    override fun getBottomCommentTipView(): View? = mCommentBottomTip
    override fun openSurveyDialog(type: Int) {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(ToolUtil.getCtx())
            return
        }
        val activity = BaseApplication.getTopActivity()
        if (activity is MainActivity) {
            val fragment = activity.currentTopFragment
            if (fragment is BaseFragment2) {
                val url = if (type == SurveyUtil.SURVEY_TYPE_AI_TIMBER) {
                    SurveyUtil.SURVEY_URL_AI_TIMBER
                } else {
                    SurveyUtil.SURVEY_URL_CONTENT
                }

                var timbre: String = ""
                if (type == SurveyUtil.SURVEY_TYPE_AI_TIMBER) {
                    timbre = getCurTtsTrackTimbreType() ?: ""
                }
                val info = viewModel.soundInfoLiveData.value
                val ySurveyDialogFragment = newInstance(
                    url,
                    info?.trackInfo?.albumId ?: 0,
                    info?.trackInfo?.trackId ?: 0,
                    timbre,
                    info?.userInfo?.uid ?: 0, type
                )
                ySurveyDialogFragment.show(
                    fragment.getChildFragmentManager(),
                    "YSurveyDialogFragment"
                )
            }
        }
    }

    private fun getCurTtsTrackTimbreType(): String? {
        // 判断当前播放声音的音色是哪一种
        val track = XmPlayerManager.getInstance(context).getCurrSoundIgnoreKind(false)
        val trackCur = viewModel.soundInfoLiveData.value?.trackInfo?.trackId
        return if (track != null && track.dataId == trackCur) {
            track.curTtsTrackTimbreType
        } else null
    }

    // 边听边看阅读器
    private fun initReadBookComponent() {
        mReadBookComponent = XReaderComponent(this).apply {
            addLifecycleListener(this)
            addPlayStatusListener(this)
            onCreate(this@YPlayFragment)
            initUI(requireView())
        }
    }

    private fun initLiveTipComponent() {
        YAnchorLivingTipComponent.newInstance(this, coverComponentsManager).apply {
            addLifecycleListener(this)
        }
    }

    private fun initCommercialPart() {
        initCommercialBroadCastComponent()
        initTrackOverAuditionDialogComponent()
        initUveClientManager()
    }

    fun getShareComponentRootView(): View? {
        return mShareComponent?.rootView
    }

    override fun showShareDialog() {
        mShareComponent?.performShare()
    }

    override fun clearPicTheme() {
        viewModel?.recoveryThemeData()
    }

    private fun initShareComponent() {
        mShareComponent = ShareComponentV3(this)
//        mShareComponent?.init()
        mShareComponent?.setShareClickListener(ShareComponentV3.IOnShareClickListener {

            var shareType : String? = null
            if (PlayPageDataManager.getInstance().soundInfo?.otherInfo?.showKnowledgeSharePackage == true) {
                shareType = "知识红包"
            }

            if (!PlayPageDataManager.getInstance().isAudioPage) {
                mShareComponent?.doShareVideo()
                mShareComponent?.traceShareBtnClick(
                    coverComponentsManager?.isFullScreen == true,
                    false,
                    shareType
                )
            } else {
                try {
                    // 当前播放是否在定制白名单
                    val isWhiteTrack =
                        PlayPageDataManager.getInstance().soundInfo.otherInfo?.showChildAiTimbre
                    // 当前使用的音色
                    val aiSoundRole = AiSoundTransformManager.getAiSoundRole()
                    //当前使用的音色不是null,并且不是波波音色0
                    if (isWhiteTrack == true && aiSoundRole != null) {
                        if (aiSoundRole.soundCategory != 0) {
                            mShareComponent?.traceShareBtnClick(
                                coverComponentsManager?.isFullScreen == true,
                                true,
                                shareType
                            )
                            mShareComponent?.showShareAiSoundDialog()
                            return@IOnShareClickListener true
                        }
                    }
                } catch (e: Throwable){
                    Logger.d(TAG, "AI声音分享获取当前播放是否在定制白名单失败")
                }


                MMKVUtil.getInstance().saveBoolean("xplay_share_tip_has_click", true)
                mShareComponent?.showShareDialog()
                mShareComponent?.traceShareBtnClick(
                    coverComponentsManager?.isFullScreen == true,
                    true,
                    shareType
                )
            }
            return@IOnShareClickListener true
        })
    }

    private fun initCoverArea() {
        XStageAreaFreqControlManager.init()
        ensureInitVideoComponent()
        coverComponentsManager = YCoverComponentsManager(
            this,
            viewModel,
            findViewById<ViewPager2>(R.id.main_play_media_pager),
            videoComponent!!,
        )
        addLifecycleListener(coverComponentsManager!!)
        coverComponentsManager?.addCoverChangeListener {
            xPlayPageScreenTimeManager?.setState(it)
        }
        coverComponentsManager?.createComponents(this)
    }

    private fun compareAndUpdate() {
        // 比较当前播放器的声音和播放页声音的点赞是否状态一致
        val info = viewModel.soundInfoLiveData.value ?: return
        val track = info.trackInfo2TrackM() ?: return
        val playableModel = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound
        if (playableModel is Track) {
            val curPlayTrack = playableModel
            if (track != null && curPlayTrack.dataId == track.dataId) {
                if (track.isLike != curPlayTrack.isLike) {
                    track.isLike = curPlayTrack.isLike
                    track.favoriteCount = curPlayTrack.favoriteCount
                    if (info.otherInfo != null) {
                        info.otherInfo!!.isLike = curPlayTrack.isLike
                        info.otherInfo!!.likeCount = curPlayTrack.favoriteCount
                    }
                }
            }
        }
//        mFunctionEntriesComponent?.checkBindDynamicLikeIcon(track)
//        mFunctionEntriesComponent?.updateLikeState(track.isLike)
//        mFunctionEntriesComponent?.updateLikeCount(track.isLike, false)
    }

    override fun videoComponent(): IVideoContainer? {
        return videoComponent
    }

    private fun ensureInitVideoComponent() {
        if (videoComponent == null) {
            videoComponent = YVideoComponent(this)
            videoComponent?.let{ component ->
                component.onCreate(this@YPlayFragment)
//                viewModel.soundInfoLiveData.value?.also {
//                    component.onSoundInfoLoaded(it)
//                }
//                addLifecycleListener(component)
            }
        }
    }

    override fun switchToVideo(startPlay: Boolean) {
//        if (isVideoMode()) return
        ensureInitVideoComponent()
        if (mReadBookComponent?.isRealShow() == true) {
            closeReadBookComponent()
        }

        //update view
        if (startPlay) {
            videoComponent?.forcePlay()
        }

        coverComponentsManager?.hideComponents()

        PlayPageDataManager.getInstance().isAudioPage = false
        XmPlayerManager.getInstance(mContext).pause(PauseReason.Business.ImmersiveModeVideo_Switch)

        notifyPlayTypeChange(isAudio = false)
        ensureScapeWidgets(com.ximalaya.ting.android.main.playpage.playx.XUtils.isLandScape(activity))

        cancelTerminatePlan()
//        YSoundQualityDialog.forceDismiss()
    }

    private fun cancelTerminatePlan() {
        if (!isVideoMode()) return
        lifecycleScope.launch {
            val shouldToast = withContext(Dispatchers.IO) {
                if (PlanTerminateManager.isTiming()) {
                    PlanTerminateManager.forceCancel()
                    true
                }else false
            }

            if (shouldToast) {
                ToastManager.showToast("视频模式暂不支持定时功能")
            }
        }
    }

    fun forceHideBottomBar() {
        mXBottomBarManageComponent?.forceHide()
    }

    override fun switchToAudio(startPlay: Boolean) {
//        if (!isVideoMode()) return

//        videoComponent?.hide()
        ViewStatusUtil.setVisible(View.VISIBLE, controlBarView, functionBarViewV2)
        videoComponent?.clearImmersive()

        PlayPageDataManager.getInstance().isAudioPage = true
        if (startPlay) XmPlayerManager.getInstance(mContext).play()
        XmPlayerManager.getInstance(context).setVideoMode(false)
        coverComponentsManager?.showComponents()
        notifyPlayTypeChange(isAudio = true)
    }

    override fun toggleControlVisibility(force: Boolean?) {
        if (!isVideoMode()) return

        TransitionManager.endTransitions(controlBarView!!)
        TransitionManager.beginDelayedTransition(controlBarView!!, Fade())
        functionBarViewV2?.let {
            TransitionManager.beginDelayedTransition(it, Fade())
        }

        val targetShow = when {
            force != null -> force
            else -> functionBarViewV2?.visibility != View.VISIBLE
        }

        if (targetShow){
            ViewStatusUtil.setVisible(View.VISIBLE, controlBarView, functionBarViewV2)
            forceShowOrHideTips(true)
            videoComponent?.toggleSeekBar(false)
        } else {
            ViewStatusUtil.setVisible(View.GONE, controlBarView, functionBarViewV2)
            forceShowOrHideTips(false)
            videoComponent?.toggleSeekBar(true)
        }

    }

    override fun getPlayModeSwitcher(): PlayModeSwitcher? {
        return switcher
    }

    override fun isVideoMode(): Boolean {
        return coverComponentsManager?.isVideoMode == true
    }

    override fun addLifecycleListener(lifecycleListener: IAudioPlayPageLifecycle) {
        mLifecycleListeners.add(lifecycleListener)
        if (lifecycleListener is XTabChangeCallback) {
            xtabSelectListeners.add(lifecycleListener)
        }
    }

    override fun removeLifecycleListener(lifecycleListener: IAudioPlayPageLifecycle) {
        mLifecycleListeners.remove(lifecycleListener)
        if (lifecycleListener is XTabChangeCallback) {
            xtabSelectListeners.remove(lifecycleListener)
        }
    }

    override fun addStickyChangeListener(listener: IStickyChangeListener) {
        mStickyChangeListeners.add(listener)
    }

    override fun removeStickyChangeListener(listener: IStickyChangeListener) {
        mStickyChangeListeners.remove(listener)
    }

    override fun addPlayStatusListener(listener: IXmPlayerStatusListener) {
        mPlayerStatusListeners.add(listener)
    }

    override fun removePlayStatusListener(listener: IXmPlayerStatusListener) {
        mPlayerStatusListeners.remove(listener)
    }

    override fun addVideoPlayStatusListener(listener: IVideoPlayStatueListener) {
        mVideoPlayerStatusListeners.add(listener)
    }

    override fun removeVideoPlayStatusListener(listener: IVideoPlayStatueListener) {
        mVideoPlayerStatusListeners.remove(listener)
    }

    private val mAdsStatusListener: IXmAdsStatusListener = object : IXmAdsStatusListenerExpand {
        override fun onGetForwardVideo(advertis: List<Advertis>?) {
            for (listener in mAdsStatusListeners) {
                if (listener is IXmAdsStatusListenerExpand) {
                    listener.onGetForwardVideo(advertis)
                }
            }
        }

        override fun onStartGetAdsInfo(playMethod: Int, duringPlay: Boolean, isPaused: Boolean) {
            notifyAdsStatusChange(Consumer { listener: IXmAdsStatusListener ->
                listener.onStartGetAdsInfo(playMethod, duringPlay, isPaused)
            })
        }

        override fun onGetAdsInfo(ads: AdvertisList?) {
            notifyAdsStatusChange(Consumer { listener: IXmAdsStatusListener ->
                listener.onGetAdsInfo(ads)
            })
        }

        override fun onAdsStartBuffering() {
            notifyAdsStatusChange(Consumer { obj: IXmAdsStatusListener -> obj.onAdsStartBuffering() })
        }

        override fun onAdsStopBuffering() {
            notifyAdsStatusChange(Consumer { obj: IXmAdsStatusListener -> obj.onAdsStopBuffering() })
        }

        override fun onStartPlayAds(ad: Advertis?, position: Int) {
            notifyAdsStatusChange(Consumer { listener: IXmAdsStatusListener ->
                listener.onStartPlayAds(ad, position)
            })
        }

        override fun onCompletePlayAds() {
            notifyAdsStatusChange(Consumer { obj: IXmAdsStatusListener -> obj.onCompletePlayAds() })
        }

        override fun onError(what: Int, extra: Int) {
            notifyAdsStatusChange(Consumer { listener: IXmAdsStatusListener ->
                listener.onError(what, extra)
            })
        }
    }

    private fun notifyAdsStatusChange(consumer: Consumer<IXmAdsStatusListener>) {
        for (listener in mAdsStatusListeners) {
            if (listener != null) {
                consumer.accept(listener)
            }
        }
    }

    fun addAdsStatusListener(listener: IXmAdsStatusListener) {
        mAdsStatusListeners.add(listener)
    }

    fun removeAdsStatusListener(listener: IXmAdsStatusListener) {
        mAdsStatusListeners.remove(listener)
    }

    private fun registerThemeColorChangeListener() {
        if (mOnThemeColorChangedListener == null) {
            mOnThemeColorChangedListener =
                IOnThemeColorChangedListener { curForegroundColor: Int, curBackgroundColor: Int ->
                    this.checkThemeColorChangeAndUpdateUI(
                        curForegroundColor,
                        curBackgroundColor
                    )
                }
        }
        PlayPageDataManager.getInstance().addThemeColorChangeListener(mOnThemeColorChangedListener)
    }

    private fun checkThemeColorChangeAndUpdateUI(curForegroundColor: Int, curBackgroundColor: Int) {
        if (curForegroundColor != mForegroundColor || curBackgroundColor != mBackgroundColor) {
            mForegroundColor = curForegroundColor
            mBackgroundColor = curBackgroundColor
            val bottomColor = PlayPageDataManager.getInstance().bottomViewBgColor
            notifyLifecycle(Consumer { lifecycle: IAudioPlayPageLifecycle ->
                lifecycle.onThemeColorChanged(
                    mForegroundColor,
                    mBackgroundColor
                )
            })
            PlayXRnCommunicationManager.getInstance().notifyRnUpdateBackgroundColor(intColorToRgba(mBackgroundColor))
        }
    }

    private fun intColorToRgba(color: Int): String {
        //获取对应的三色
        val red: Int = color and 0xff0000 shr 16
        val green: Int = color and 0x00ff00 shr 8
        val blue: Int = color and 0x0000ff
        return "rgb($red,$green,$blue)"
    }

    private fun notifyLifecycle(consumer: Consumer<IAudioPlayPageLifecycle>) {
        for (lifecycle in mLifecycleListeners) {
            if (lifecycle != null) {
                consumer.accept(lifecycle)
            }
        }
    }

    override fun loadData() {
        setLastTrackHasDoc(false)
    }

    private fun loadData(isForce: Boolean) {
        PlayPageDataManager.getInstance().loadSoundInfo(object : IDataCallBack<PlayingSoundInfo> {
            override fun onSuccess(data: PlayingSoundInfo?) {
                // do Nothing for now
            }

            override fun onError(code: Int, message: String?) {
                // do Nothing for now
            }
        }, isForce)
    }

    override fun getContainerLayoutId() = R.layout.main_play_page_y

    override fun inflateViewInner(
        inflater: LayoutInflater,
        layoutId: Int,
        container: ViewGroup?,
        attachToRoot: Boolean
    ): View? {
        return try {
            if (PageStartOpt.PlayPageOpt.enable()) {
                PageStartOpt.PlayPageOpt
                    .getViewForFragment(inflater, layoutId, container, attachToRoot, javaClass.canonicalName)
            } else inflater.inflate(layoutId, container, attachToRoot)
        } catch (t: Throwable) {
            throw t
        }
    }


    override fun onTrackChangedWhileResume() {
        AnimationManagerV2.reset()
//        loadData()
        gotoTop()
    }

    override fun getTagIdInBugly() = 236146

    private var lastCheckId = 0L
    private val cancelForbidden = Runnable {
        bizArbitersForbiddenFinish = true
        // 非阅读器模式时
        if (mReadBookComponent?.isRealShow() == false) {
            bizArbiters?.borbidden(false)
        }
    }
    private var bizArbitersForbiddenFinish = true

    private var mCanShowStageCard: Boolean = !(YUtils.getForbiddenConfig()?.cardLimit ?: false)
    private val mCardTimeCountTask = Runnable {
        Logger.d(TAG, "mCanShowStageCard = true")

        mCanShowStageCard = true
        coverComponentsManager?.updateComponents()
    }

    private fun forbiddenShowTrashCheck() {
        if (lastCheckId == 0L || ::viewModel.isInitialized && lastCheckId != viewModel.trackId) {
            if (::viewModel.isInitialized) lastCheckId = viewModel.trackId

            val config = YUtils.getForbiddenConfig()

            // 弹窗检查
            HandlerManager.removeCallbacks(cancelForbidden)
            if (config != null && config.popoverLimit == true) {
                bizArbitersForbiddenFinish = false
                bizArbiters?.borbidden(true)
                HandlerManager.postOnUIThreadDelay(cancelForbidden, (config.limitSeconds?: 5) * 1000L)
            }

            // 卡片检查
            HandlerManager.removeCallbacks(mCardTimeCountTask)
           if (config != null && config.cardLimit == true) {
               mCanShowStageCard = false
               Logger.d(TAG, "mCanShowStageCard = false; config.limitSeconds = ${config.limitSeconds}")
               HandlerManager.postOnUIThreadDelay(mCardTimeCountTask, (config.limitSeconds ?: 5) * 1000L)
           } else {
               Logger.d(TAG, "mCanShowStageCard: config is null or empty; ignore control")
           }
        }
    }

    override fun canShowStageAreaCard(): Boolean {
        return mCanShowStageCard
    }

    override fun showReadBookComponent(enterFrom: Int, selectModel:Boolean) {
        mReadBookComponent?.setSelectModel(selectModel)
        mReadBookComponent?.setEnterFrom(enterFrom)
        mReadBookComponent?.xshow()
    }

    // 从边听边看切换到播放页时
    override fun closeReadBookComponent() {
//        val fragment = FragmentUtil.getShowingFragmentByClass(
//            activity as FragmentActivity,
//            PlayFragmentNew::class.java
//        )
//        if (fragment != null && fragment is PlayFragmentNew && fragment.isRealVisable) {
//            fragment.doBack()
//        }
        forbiddenBizArbiters(false)
        mReadBookComponent?.xhide()
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).setIsTingTextVisible(false)
        XmPlayerManager.getInstance(mContext).setPlayFragmentIsShowing(isRealVisible)
    }

    override fun canShowDanMuEntry(): Boolean {
        return coverComponentsManager?.curMainComponentCanShowDanMuBtn() ?: false
    }

    override fun getPlayPageScreenShot(): Bitmap? {
        if (requireView() == null) {
            return null
        }

        return TingTextToReaderManager.getScreenShot(requireView()!!)
    }

    override fun forbiddenBizArbiters(forbidden: Boolean) {
        if (forbidden) {
            bizArbiters?.borbidden(true)
        } else {
            // 播放页前5秒禁止弹窗，5秒内无需设置，有cancel线程设置
            if (bizArbitersForbiddenFinish) {
                bizArbiters?.borbidden(false)
            }
        }
    }

    // 边听边看模式禁止任何弹窗展示
    override fun dismissAllBizArbiters() {
        bizArbiters?.dismissAll()
    }

    override fun addPptModeSwitch(listener: IPptModeSwitchListener) {
        mPprModeSwitchChangedListener?.add(listener)
    }

    override fun removePptModeSwitch(listener: IPptModeSwitchListener) {
        mPprModeSwitchChangedListener?.remove(listener)
    }

    override fun notifyPptModeSwitch(pptMode: Boolean) {
        mPprModeSwitchChangedListener?.forEach {
            it.onPptModeSwitch(pptMode)
        }
    }

    override fun onMyResume() {
        super.onMyResume()
        isForceExitLandscape = false
        StatusBarManager.setStatusBarColor(window, false)
        forbiddenShowTrashCheck()
        PlayAdFrequencyMonitor.onAudioPageShow()
//        checkVideoStatus()

        if (::viewModel.isInitialized && !viewModel.isFromBack && !viewModel.fromPlayBar) {
            if (isFullScreen() && !isVideoMode() && !YUtils.isLandScape(context)) {
                toggleFullscreen("音频全半屏切换")
            }
        }

        val mSoundInfo = viewModel.soundInfoLiveData.value
        if (mSoundInfo != null) {
            // 新声音播放页  页面展示
            XMTraceApi.Trace()
                .pageView(17444, "newPlay", this) // 页面出现在用户视野时上报一条埋点，包括离开页面后返回、息屏后亮屏等
                .put("albumId", PlayCommentUtil.getCurAlbumId(mSoundInfo).toString())
                .put("trackId", PlayCommentUtil.getCurTrackId(mSoundInfo).toString())
                .put("anchorId", PlayCommentUtil.getCurAnchorId(mSoundInfo).toString())
                .put("categoryId", PlayCommentUtil.getCurCategoryId(mSoundInfo).toString())
                .put("currTrackId", PlayCommentUtil.getCurTrackId(mSoundInfo).toString())
                .put("currAlbumId", PlayCommentUtil.getCurAlbumId(mSoundInfo).toString())
                .put("currPage", "newPlay")
                .put("style", "舞台区")
                .put("payCourseRight", mSoundInfo.trackInfo?.isAuthorized?.toString() ?: "false")
                .createTrace()
        }
        // 此行代码需要放到前面
        XmPlayerManager.getInstance(mContext).setPlayFragmentIsShowing(true)
        mLiveAndMcEntryComponent?.onFragmentResume()
        checkChildProtectMode()
        registerThemeColorChangeListener()
        XmPlayerManager.getInstance(context).addAdsStatusListener(mAdsStatusListener)
        mLifecycleListeners.forEach { it.onMyResumeOnly() }
        mLifecycleListeners.forEach { it.onResume() }
        compareAndUpdate()

        AdMakeVipLocalManager.getInstance().setPlayPageVisible(true)

        HandlerManager.postOnUIThreadDelay( {
            if (PlayListAndHistoryDialogManager.isNeedOpen()) {
                Log.d(TAG, "try show playList")
                showPlayList()
            }
        }, 300)

        HandlerManager.postOnUIThreadDelay({
            checkShowAwardHintDialog()
        }, 500)

        YUtils.hideStatusBarIfLandScape(mActivity)

        if (TempDataManager.getInstance().getLong(BundleKeyConstants.KEY_XPLAY_FRAGMENT_OPEN_COMMENT_TRACK_ID) != 0L) {
            HandlerManager.postOnUIThreadDelay({
                if (canUpdateUi()) {
                    if ((mSoundInfo != null && YUtils.isNoTab(mSoundInfo)) || (ConstantsOpenSdk.isDebug && BaseUtil.getIntSystemProperties("debug.show.imdependent.comment.mode") > 0)) {
                        openIndependentCommentPage(false)
                    } else {
                        openFloatingCommentPanel(
                            needInputTimeMark = isAudioPage(),
                            enableAutoShowKeyboard = false
                        )
                    }
                }
                TempDataManager.getInstance().removeLong(BundleKeyConstants.KEY_XPLAY_FRAGMENT_OPEN_COMMENT_TRACK_ID)
            }, 300)
        }

        viewModel.iTingLiveData.value?.targetTabId?.let { iTingTabId ->
            if (iTingTabId == 2L) {
                viewModel.soundInfoLiveData.value?.let {
                    if (YUtils.isNoTab(it)) {
                        openIndependentCommentPage(false, 0)
                        viewModel.setIting("")
                    }
                }
            }
        }
        cancelTerminatePlan()
    }


    override fun onUserLoginStatusChangedAndNeedRefreshData() {
        yTabsComponent?.onUserLoginStatusChangedAndNeedRefreshData()
    }

    private fun checkChildProtectMode() {
        val mode = ChildProtectManager.isChildProtectOpen(mContext)
        if (mIsChildProtectMode == mode) {
            return
        }
        mIsChildProtectMode = mode
    }

    override fun onPause() {
        super.onPause()
        if (YUtils.isLandScape(context)) {
            isForceExitLandscape = true
            exitLandScape()
        }

        //reset forbidden check id
        if (::viewModel.isInitialized) lastCheckId = viewModel.trackId

        AnimationManagerV2.reset()
        if (PadAdaptUtil.isPad(context)) {
            //activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR
        }
        val mSoundInfo = viewModel.soundInfoLiveData.value
        if (mSoundInfo != null) {
            XMTraceApi.Trace()
                .pageExit2(17445, this)
                .put("currPage", "newPlay")
                .put("albumId", PlayCommentUtil.getCurAlbumId(mSoundInfo).toString())
                .put("trackId", PlayCommentUtil.getCurTrackId(mSoundInfo).toString())
                .put("anchorId", PlayCommentUtil.getCurAnchorId(mSoundInfo).toString())
                .put("categoryId", PlayCommentUtil.getCurCategoryId(mSoundInfo).toString())
                .put("currTrackId", PlayCommentUtil.getCurTrackId(mSoundInfo).toString())
                .put("currAlbumId", PlayCommentUtil.getCurAlbumId(mSoundInfo).toString())
                .createTrace()
        }

        if (mSoundInfo?.trackInfo?.isVideo == true) {
            saveCurTrackLastShowStateToMMKV(PlayCommentUtil.getCurTrackId(mSoundInfo))
        }
        // 需要放到前面
        XmPlayerManager.getInstance(mContext).setPlayFragmentIsShowing(false)
        mLiveAndMcEntryComponent?.onFragmentPause()
        mShareComponent?.onPause()
        XmPlayerManager.getInstance(context).removeAdsStatusListener(mAdsStatusListener)
        AdMakeVipLocalManager.getInstance().setPlayPageVisible(false)
        PlayPageDataManager.getInstance().removeThemeColorChangeListener(mOnThemeColorChangedListener)

        bizArbiters?.dismissAllOnPause()

        if (isVideoMode() && videoComponent?.isPlaying() == true) {
            lifecycleScope.launch {
                withContext(Dispatchers.Main) {
                    delay(500)
                    XmPlayerManager.getInstance(context).play()
                }
            }
        }
        mLifecycleListeners.forEach { it.onPause() }
    }

    private fun saveCurTrackLastShowStateToMMKV(trackId: Long) {
        if (trackId > 0) {
            MMKVUtil.getInstance().saveBoolean(
                PreferenceConstantsInMain.KEY_PLAY_PAGE_LAST_SHOW_PAGE + trackId,
                isVideoMode()
            )
        }
    }

    /**
     * 声音离开播放页时是否是视频状态
     */
    private fun isVideoModeLastShow(trackId: Long): Boolean {
        return MMKVUtil.getInstance()
            .getBoolean(PreferenceConstantsInMain.KEY_PLAY_PAGE_LAST_SHOW_PAGE + trackId)
    }

    override fun onDestroy() {
        super.onDestroy()
        mCommercialCore?.onDestroy()
        UveClientManager.getInstance().clearAllBusiness()
        playPageApiForTab?.setTabScrollable(true)
        mLiveAndMcEntryComponent?.onFragmentDestroy()
        mAdService?.unregisterAdCoverStateChange(this)
        mAdService = null
        XPlayPageRef.setXPlayPage(null)

        // add exception handler
        runCatching {
            mLifecycleListeners.forEach { it.onDestroy() }
        }.getOrElse {
            CrashReport.postCatchedException(it)
        }

        Logger.d("PlayPageOpt_", "YPlayFragment onDestroy: $delayNotifyDataRunnable")
        if (delayNotifyDataRunnable != null) {
            HandlerManager.removeCallbacks(delayNotifyDataRunnable)
        }

        mCommentManager?.release()
        AnchorFollowManage.getSingleton().removeFollowListener(mFollowAnchorListener)
        LikeTrackManage.removeListener(mLikeStateChange)
        TrackCollectManager.getInstance().removeListener(mCollectStateChange)
        AlbumEventManage.removeListener(mAlbumFavoriteChange)
        TrackCommentBehaviorFactory.newInstance().removeManager(mTrackCommentBehaviorListener)
        PlayPageControlManager.removePlayControlChangeListener(this)
        PlayPageControlManager.release()
        PlayXRnCommunicationManager.getInstance().release()
        coverComponentsManager?.bottomBarShowChangeListener = null
        reset()
        unregisterAudioPlayFragmentService()
        mCoverChangeListenerSet?.clear()
        mVideoPlayerStatusListeners?.clear()
        mDanmakuIntputBar?.setIDanmakuOpenStatusChangeListener(null)
        XmPlayerManager.getInstance(context).setVideoMode(false)
        mScreenOrientationChanges.clear()
        YPlayPageYellowBarManager.instance.release()
        PlayPageWifiIntranetManager.destroy()
    }

    override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
        super.onSoundSwitch(lastModel, curModel)
        AnimationManagerV2.reset()

        toggleControlVisibility(true)
        videoComponent?.clearImmersive()

        bizArbiters?.dismissAll(ignorePlayList = true)
        coverComponentsManager?.updateComponents()
        videoComponent?.onSoundSwitch(lastModel, curModel)
        mCurTrackId = curModel?.dataId ?: 0
        loadData()
        gotoTop()
//        xTabsComponent?.switchToFirst()

        notifyPlayStatusChange(Consumer { listener: IXmPlayerStatusListener ->
            listener.onSoundSwitch(
                lastModel,
                curModel
            )
        })

        if ((curModel as? Track)?.isVideo == true) {
            videoComponent?.updateVideoId(curModel)
        }
    }

    override fun gotoTop(expand: Boolean, animate: Boolean, once: Boolean) {

        clearSticky()
        if (::topAppbarLayout.isInitialized) {
            topAppbarLayout.setExpanded(expand, animate, once)
        }
        yTabsComponent?.scrollToTop()
    }

    /**
     * 正在显示待播引导
     * */
    override fun isShowingToListenGuide(): Boolean {
        return false
    }

    override fun hideAllXTips() {
//        if (xTipsManager == null) {
//            return
//        }
//        xTipsManager!!.checkVisible()
    }


    override fun onPlayStart() {
        mPlayerStatusListeners.forEach { it.onPlayStart() }
        if (isVideoMode() && BaseUtil.isForegroundIsMyApplication(activity)) {
            //XmPlayerManager.getInstance(context).pause()
        }
    }

    override fun onPlayPause() {
        notifyPlayStatusChange(Consumer { obj: IXmPlayerStatusListener -> obj.onPlayPause() })
    }

    override fun onPlayStop() {
        notifyPlayStatusChange(Consumer { obj: IXmPlayerStatusListener -> obj.onPlayStop() })
    }

    override fun onSoundPlayComplete() {
        notifyPlayStatusChange(Consumer { obj: IXmPlayerStatusListener -> obj.onSoundPlayComplete() })
    }

    override fun onSoundPrepared() {
        notifyPlayStatusChange(Consumer { obj: IXmPlayerStatusListener -> obj.onSoundPrepared() })
    }

    override fun onBufferingStart() {
        notifyPlayStatusChange(Consumer { obj: IXmPlayerStatusListener -> obj.onBufferingStart() })
    }

    override fun onBufferingStop() {
        notifyPlayStatusChange(Consumer { obj: IXmPlayerStatusListener -> obj.onBufferingStop() })
    }

    override fun onBufferProgress(percent: Int) {
        notifyPlayStatusChange(Consumer { listener: IXmPlayerStatusListener ->
            listener.onBufferProgress(
                percent
            )
        })
    }

    override fun onPlayProgress(currPos: Int, duration: Int) {
        notifyPlayStatusChange(Consumer { listener: IXmPlayerStatusListener ->
            listener.onPlayProgress(
                currPos,
                duration
            )
        })
    }

    override fun onError(exception: XmPlayerException?): Boolean {
        notifyPlayStatusChange(Consumer { listener: IXmPlayerStatusListener ->
            listener.onError(
                exception
            )
        })
        return false
    }

    override fun onRequestPlayUrlBegin() {
        notifyExtensionPlayStatusChange(Consumer { obj: IXmPlayerStatusListenerExtension -> obj.onRequestPlayUrlBegin() })
    }

    override fun onRequestPlayUrlSuccess() {
        notifyExtensionPlayStatusChange(Consumer { obj: IXmPlayerStatusListenerExtension -> obj.onRequestPlayUrlSuccess() })
    }

    override fun onRequestPlayUrlError(code: Int, message: String?) {
        notifyExtensionPlayStatusChange(Consumer { listener: IXmPlayerStatusListenerExtension ->
            listener.onRequestPlayUrlError(
                code,
                message
            )
        })
    }

    // 试听结束
    override fun onAudioAuditionOver(track: Track?) {
        notifyExtensionPlayStatusChange(Consumer { listener: IXmPlayerStatusListenerExtension ->
            listener.onAudioAuditionOver(
                track
            )
        })
    }


    private fun notifyPlayStatusChange(consumer: Consumer<IXmPlayerStatusListener>) {
        for (listener in mPlayerStatusListeners) {
            if (listener != null) {
                consumer.accept(listener)
            }
        }
    }

    private fun notifyExtensionPlayStatusChange(consumer: Consumer<IXmPlayerStatusListenerExtension>) {
        for (listener in mPlayerStatusListeners) {
            if (listener is IXmPlayerStatusListenerExtension) {
                consumer.accept(listener)
            }
        }
    }

    override fun isAudioMode(): Boolean {
        return isAudioPage()
    }

    override fun dismissDialog() {
        bizArbiters?.dismissAll()
    }

    override fun showCommentPanel(needInputTimeMark: Boolean, enableAutoShowKeyboard: Boolean) {
        openFloatingCommentPanel(needInputTimeMark, enableAutoShowKeyboard)
    }

    override fun showMasterClassNotePanel() {
        openMasterClassNoteInputPanel()
    }

    override fun startInputComment(title: String?, fromSource: Int) {
        val soundInfo = viewModel.soundInfoLiveData.value ?: return
        val trace = XMTraceApi.Trace()
            .put("anchorId", PlayCommentUtil.getCurAnchorId(soundInfo).toString())
            .put("currTrackId", PlayCommentUtil.getCurTrackId(soundInfo).toString())
            .put("currAlbumId", PlayCommentUtil.getCurAlbumId(soundInfo).toString())
            .put("uid", UserInfoMannage.getUid().toString())
        if (yTabsComponent?.checkToggleCommentInput(fromSource) != true) { // 当前tab下如果无法打开评论输入框，则打开评论浮层
            trace.put("tabName", title ?: "详情")
            openFloatingCommentPanel(fromSource = fromSource)
        } else {
            trace.put("tabName", "评论")
        }
        CommentMarkPointUtil.traceFor42015(
            trace,
            CommentConstants.FROM_PAGE_AUDIO,
            CommentConstants.COMMENT_TYPE_SEND_COMMENT
        )
    }

    private fun openIndependentCommentPage(
        enableAutoShowKeyboard: Boolean = true,
        anchorCommentId: Long = 0
    ) {
        val fragment = YPlayCommentIndependentFragment.newInstanceForTrack(
            CommentConstants.FROM_PAGE_AUDIO,
            PlayTools.getCurTrackId(mContext),
            isVideoMode()
        )
        if (anchorCommentId > 0) {
            fragment.anchorCommentById(anchorCommentId)
        }
        playPageApiForTab?.showFragmentOnPlayPage(fragment)
    }

    private fun openFloatingCommentPanel(needInputTimeMark: Boolean = isAudioPage(),
                enableAutoShowKeyboard: Boolean = true, anchorCommentId: Long = 0, fromSource: Int = -1) {
        val soundInfo = viewModel.soundInfoLiveData.value ?: return
        val playPageApiForTab = playPageApiForTab ?: return
        val hasNoComment = soundInfo.trackInfo?.comments?.let { it <= 0 } == true
        val targetFragment: FloatingTrackCommentFragment
        val existFragment = playPageApiForTab.getChildFragmentOnPlayPage(object : Function1<Fragment, Boolean> {
            override fun invoke(childFra: Fragment): Boolean {
                return childFra is FloatingTrackCommentFragment
                            && childFra.getListType() == CommentListView.TYPE_TRACK_COMMENT
                            && childFra.getTrackId() == PlayPageDataManager.getInstance().curTrackId
                }
            }) as? FloatingTrackCommentFragment
        if (existFragment == null) {
            val isAutoShowKeyboard = enableAutoShowKeyboard
                    && CommentHintTool.isAllowComment(PlayCommentUtil.getAllowCommentType(soundInfo))
            if (isAutoShowKeyboard) {
                var initialInputText: String? = null
                var initialInputTemplateId: Long = 0
                if (hasNoComment) {
                    val existHint = CommentHintTool.getExistHintWithTemplateId(
                        PlayPageDataManager.getInstance().curTrackId
                    )
                    if (existHint != null) {
                        initialInputTemplateId = existHint.first
                        initialInputText = existHint.second
                    }
                }
                targetFragment = newInstanceForTrack(
                    CommentConstants.FROM_PAGE_AUDIO,
                    PlayTools.getCurTrack(mContext),
                    isAutoShowKeyboard = true,
                    anchorCommentId,
                    initialInputText,
                    initialInputTemplateId,
                    useComplexHeader = true,
                    canShowAdvertisements = true,
                    fromSource = fromSource,
                    fromAudioAndPlayPage = true
                )
            } else {
                targetFragment = newInstanceForTrack(
                    CommentConstants.FROM_PAGE_AUDIO, PlayTools.getCurTrack(mContext), false,
                    anchorCommentId = anchorCommentId, useComplexHeader = true,
                    canShowAdvertisements = true,
                    fromSource = fromSource,
                    fromAudioAndPlayPage = true
                )
            }
            targetFragment.setOnDismissListener(object : IFloatingFragmentDismissListener {
                override fun onDismiss(floatingFragment: BaseFragment2) {
                    playPageApiForTab.hideFragmentOnPlayPage(floatingFragment)
                    HandlerManager.postOnUIThread {
                        updateCommentInputHint()
                    }
                }
            })
        } else {
            targetFragment = existFragment
            if (anchorCommentId > 0) {
                existFragment.anchorCommentById(anchorCommentId)
            }
//            if (enableAutoShowKeyboard && hasNoComment && existFragment.canUpdateUi()) {
//                val existHint = CommentHintTool.getExistHintWithTemplateId(
//                    PlayPageDataManager.getInstance().curTrackId
//                )
//                if (existHint != null) {
//                    existFragment.inputInitialCommentHint(existHint.first, existHint.second)
//                }
//                HandlerManager.postOnUIThreadDelay({
//                    if (existFragment.canUpdateUi() && !existFragment.isHidden) {
//                        existFragment.toggleCommentInput()
//                    }
//                }, 500)
//            }
        }
        targetFragment.showTimeMarkWhenInput(needInputTimeMark)
        playPageApiForTab.showFragmentOnPlayPage(targetFragment)
    }

    override fun openMasterClassNoteInputPanel() {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mContext)
            return
        }
        val soundInfo = viewModel.soundInfoLiveData.value ?: return
        val playPageApiForTab = playPageApiForTab ?: return
        if (soundInfo.trackInfo?.isAuthorized != true) {
            if (activity is MainActivity && soundInfo.trackInfo != null) {
                ToolUtil.clickUrlAction(
                    activity as MainActivity,
                    MasterClassNoteEventManager.getInstance().getBuyMasterVipUrl(
                        soundInfo.trackInfo?.albumId ?: 0,
                        soundInfo.trackInfo?.trackId ?: 0
                    ),
                    null
                )
            }
            return
        }
        val targetFragment: MasterClassNoteCreateFragment
        val existFragment =
            playPageApiForTab.getChildFragmentOnPlayPage(object : Function1<Fragment, Boolean> {
                override fun invoke(childFra: Fragment): Boolean {
                    return childFra is MasterClassNoteCreateFragment
                            && childFra.getTrackId() == PlayPageDataManager.getInstance().curTrackId
                }
            }) as? MasterClassNoteCreateFragment
        if (existFragment == null) {
            targetFragment = MasterClassNoteCreateFragment.newInstanceForTrack(
                track = soundInfo.trackInfo2TrackM()
            )
            targetFragment.setOnDismissListener(object : IFloatingFragmentDismissListener {
                override fun onDismiss(floatingFragment: BaseFragment2) {
                    playPageApiForTab.hideFragmentOnPlayPage(floatingFragment)
                    HandlerManager.postOnUIThread {
                        updateCommentInputHint()
                    }
                }
            })
        } else {
            targetFragment = existFragment
        }
        playPageApiForTab.showFragmentOnPlayPage(targetFragment)
    }

    fun updateCommentInputHint() {
        mXBottomBarManageComponent?.updateCommentInputHint()
    }

    private fun createTrackIdProvider(): PlayCommentManagerNew.ITrackIdProvider? {

        return object : PlayCommentManagerNew.ITrackIdProvider {
            override fun getCurTrackId(): Long {
                return trackId
            }

            override fun getCurAlbumId(): Long {
                return PlayCommentUtil.getCurAlbumId(viewModel.soundInfoLiveData.value)
            }

            override fun getCurCategoryId(): Long {
                return PlayCommentUtil.getCurCategoryId(viewModel.soundInfoLiveData.value)
            }

            override fun getAnchorId(): Long {
                return PlayCommentUtil.getCurAnchorId(viewModel.soundInfoLiveData.value)
            }

            override fun getTaskIdAndReset(): Long {
                return if (playPageApiForTab != null) {
                    playPageApiForTab.taskIdAndReset
                } else 0
            }

            override fun getTrackCover(): String {
                return PlayCommentUtil.getTrackCover(viewModel.soundInfoLiveData.value).toString()
            }
        }
    }

    private val mFollowAnchorListener = IFollowAnchorListener { uid, follow ->
        val soundInfo = viewModel.soundInfoLiveData.value
        if (soundInfo?.userInfo?.uid != uid) return@IFollowAnchorListener
        soundInfo.otherInfo?.let {
            if (soundInfo?.userInfo?.uid == uid) {
                it.isFollowed = follow
                if (follow) {
                    if (it.allowCommentType == PlayingSoundInfo.OtherInfo.ALLOW_COMMENT_TYPE_LEVEL_2) {
                        it.allowCommentType = PlayingSoundInfo.OtherInfo.ALLOW_COMMENT_TYPE_LEVEL_1
                        it.allowCommentTypeDesc = ""
                    }
                    mBottomCommentBar?.updateQuoraInputText()
                    mBottomPlayBar?.updateQuoraInputText()
                }
                mYNavigationBarComponent?.onSoundInfoLoaded(soundInfo)
            }
        }
    }

    private val mTrackCommentBehaviorListener: ITrackCommentBehaviorListener =
        object : ITrackCommentBehaviorListener {
            override fun requestHandleCommentAdded(
                commentType: Int,
                model: CommentModel?,
                rootCommentModel: CommentModel?,
                notifyFromPage: Int
            ) {
                if (model == null || model.id <= 0 || model.trackId != viewModel.trackId) {
                    return
                }

                viewModel.soundInfoLiveData.value?.let { soundInfo ->

                    soundInfo.trackInfo?.let { trackInfo ->
                        if (trackInfo.trackId == model.trackId) {
                            if (soundInfo.otherInfo?.allowCommentType == PlayingSoundInfo.OtherInfo.ALLOW_COMMENT_TYPE_LEVEL_1 || ConfigAbUtil.canAddCommentByTopicWhenNoAllow()) {
                                trackInfo.comments++
                            }
                            mBottomCommentBar?.onSoundInfoLoaded(soundInfo)
                            mBottomPlayBar?.onSoundInfoLoaded(soundInfo)
                        }
                    }
                }
            }

            override fun requestHandleCommentDeleted(
                commentType: Int,
                model: CommentModel?,
                rootCommentModel: CommentModel?
            ) {
                if (model == null || model.id <= 0 || model.trackId != viewModel.trackId) {
                    return
                }
                viewModel.soundInfoLiveData.value?.let { soundInfo ->
                    soundInfo.trackInfo?.let { trackInfo ->
                        if (trackInfo.trackId == model.trackId) {
                            trackInfo.comments = (trackInfo.comments - 1 - (if (model.parentId <= 0) model.replyCount else 0)).coerceAtLeast(0)
                            mBottomCommentBar?.onSoundInfoLoaded(soundInfo)
                            mBottomPlayBar?.onSoundInfoLoaded(soundInfo)
                        }
                    }
                }
            }

            override fun requestHandleCommentModelLikeState(model: CommentModel?, like: Boolean) {}
            override fun requestHandleCommentModelHateState(model: CommentModel?, hate: Boolean) {}
            override fun requestHandleCommentRefresh() {}
            override fun notifyAllManagersHasAd(
                hasColumnLargeCoverAdComponent: Boolean,
                trackId: Long
            ) {
            }

            override fun notifyTrackCommentsLoaded(trackId: Long) {}
        }


    override fun addScreenChangeListener(listener: ScreenChangeListener) {

    }

    override fun removeScreenChangeListener(listener: ScreenChangeListener) {

    }

    override fun addPlayTypeChangeListener(listener: PlayTypeChangeListener) {
        playTypeChangeListeners.add(listener)
    }

    override fun removePlayTypeChangeListener(listener: PlayTypeChangeListener) {
        playTypeChangeListeners.remove(listener)
    }

    private fun notifyPlayTypeChange(isAudio: Boolean) {
        MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInHost.KEY_USER_PLAY_LAST_IS_AUDIO, isAudio)
        viewModel.soundInfoLiveData.value?.also {
            coverComponentsManager?.onSoundInfoLoaded(it)
        }

        playTypeChangeListeners.forEach { it.onPlayTypeChanged(isAudio) }
    }

    override fun updateCoverComponents() {
        coverComponentsManager?.updateComponents()
    }

    override fun getTitleBarPositionY(): Int {
        return mTitleBarHeight
    }

    override fun getOriginalBookLocationX(): Float {
        return (mYNavigationBarComponent?.getOriginalBookTvLocationX() ?: (BaseUtil.getScreenWidth(mContext) - 16.dp)).toFloat()
    }

    override fun registerScrollListener(scrollListener: IScrollListenerCallBack) {
        mScrollListenerSet?.add(scrollListener)
    }

    override fun removeScrollListener(scrollListener: IScrollListenerCallBack) {
        mScrollListenerSet?.remove(scrollListener)
    }

    override fun addShowSkipHeadTailListener(skipHeadListener: ISkipHeadTailShowListener) {
        mSkipHeadTailShowListenerSet?.add(skipHeadListener)
    }

    override fun removeShowSkipHeadTailListener(skipHeadListener: ISkipHeadTailShowListener) {
        mSkipHeadTailShowListenerSet?.remove(skipHeadListener)
    }

    override fun notifySkipShowListener(isShow: Boolean, isAudioTips: Boolean) {
        mSkipHeadTailShowListenerSet?.forEach {
            it?.onShowSkipHeadTail(isShow, isAudioTips)
        }
    }

    override fun currentTab(): XPlayPageTab? {
        return yTabsComponent?.currentTab
    }

    override fun showSeekbarAndFunctionForce() {
        if (isFullScreen()) {
            mControlBar?.showSeekbarPart()
        }
    }

    override fun isShowVideoEntry(): Boolean {
        return mYNavigationBarComponent?.isShowVideoEntry()?:false
    }

    fun notifyTabSelect(tab: XPlayPageTab) {
        xtabSelectListeners.forEach {
            it.onTabSelect(tab)
        }
    }

    fun notifyTabUnSelect(tab: XPlayPageTab) {
        xtabSelectListeners.forEach {
            it.onTabUnSelect(tab)
        }
    }

    private fun notifyScrollListener(state: Int) {
        mScrollListenerSet.forEach {
            it?.onScrollStateChanged(null, state)
        }
    }

    override fun isFullScreen(): Boolean {
        return this.fullScreen
    }

    override fun updateNextAndPreBtnStatus(hasNext: Boolean, hasPre: Boolean) {
        videoComponent?.updateNextAndPreBtnStatus(hasNext, hasPre)
    }

    private fun showLiveAndMcEntry(soundInfo: PlayingSoundInfo?) {
        // 由于有广告回调会调用这个方法，可能在声音切换后而soundinfo没加载前就调用，这时候soundinfo和正在播放的声音是不一致的，不进行更新
        if (soundInfo?.trackInfo?.trackId == PlayPageDataManager.getInstance().curTrackId) {
            // 如果后面右上角再加新的话，需要改下实现方式。定义基类和优先级，按优先级遍历出。
            if (!mIsChildProtectMode && viewModel.fromVideoChannelLiveData.value != true) {
                mLiveAndMcEntryComponent?.showLive(
                    soundInfo, object : XLiveAndMcEntryComponent.ILoadLiveDataCallback {
                        override fun showLiveEntry(show: Boolean) {
                            if (!show) {
                                hideLiveAndMcEntryComponent(false)
                            }
                        }

                        override fun shouldShowTips(): Boolean {
                            return true
                        }
                    }
                )
            } else {
                hideLiveAndMcEntryComponent(true)
            }
        }
    }

    private fun hideLiveAndMcEntryComponent(hideAll: Boolean) {
        mLiveAndMcEntryComponent?.hide(hideAll)
    }

    private fun registerAudioPlayFragmentService() {
        PlayPageInternalServiceManager.getInstance().registerService(IXPlayFragmentService::class.java,
                object : IXPlayFragmentService {
                    override fun registerLifecycleCallback(lifecycle: IAudioPlayPageLifecycle) {
                        <EMAIL>(lifecycle)
                    }

                    override fun registerScrollListener(scrollListener: IScrollListenerCallBack) {
                        <EMAIL>(scrollListener)
                    }

                    override fun removeScrollListener(scrollListener: IScrollListenerCallBack) {
                        <EMAIL>(scrollListener)
                    }

                    override fun getBottomBarHeight(): Int {
                        return if (mBottomBarContainer != null) {
                            mBottomBarContainer!!.height
                        } else {
                            BaseUtil.dp2px(context, 60f)
                        }
                    }

                    override fun isVideoMode(): Boolean {
                        return <EMAIL>()
                    }

                    override fun getVideoProgress(): Int {
                        return <EMAIL>?.currentProgress() ?: 0
                    }

                    override fun getVideoDuration(): Int {
                        return <EMAIL>?.duration() ?: 0
                    }

                    override fun isVideoPlaying(): Boolean {
                        return <EMAIL>?.isPlaying() ?: false
                    }

                    override fun fullMode(): String {
                        return if (YUtils.isLandScape(context)) {
                            "landscape"
                        } else {
                            if (isFullScreen()) {
                                "full"
                            } else {
                                "half"
                            }
                        }
                    }
                }
            )
    }

    private fun unregisterAudioPlayFragmentService() {
        PlayPageInternalServiceManager.getInstance().unRegisterService(IXPlayFragmentService::class.java)
    }

    override fun onPlayPageControlActivate(isActivate: Boolean) {

    }

    override fun onStageAreaSizeChanged(targetHeight: Int, isAdExpand: Boolean) {
        mTopLayoutHeight = targetHeight
        if (!isFullScreen() && isTopStick == false) {
            resizeScreenLayout(targetHeight)
        }
    }

    fun getTopHeight(): Int {
        return mTopLayoutHeight
    }

    override fun resizeScreenLayout(height: Int) {
//        mTopLayoutHeight = height
        Log.d(TAG, "resizeScreenLayout ${BaseUtil.px2dip(context, height.toFloat())}")

//        if (isFullScreen()) {
//            return
//        }
//        val layoutParams = audioContainer.layoutParams ?: return
//        if (layoutParams.height != height) {
//            layoutParams.height = height
//            audioContainer.requestLayout()
//        }
    }

    /**
     * 是否是音频界面
     */
    fun isAudioPage(): Boolean {
        return !isVideoMode()
    }

    override fun canShowFloatingControlBar(): Boolean {
        return false
    }

    override fun onAdCoverHide() {
        if (canUpdateUi()) {
            end(AnimateBiz.AD)
        }
    }

    override fun noAdCover() {
        if (canUpdateUi()) {
            end(AnimateBiz.AD)
        }
    }

    override fun onAdCoverShow() {
    }

    private val mLikeStateChange = object : LikeTrackManage.TrackLikeStatusListener {
        override fun onTrackLikeChanged(isLikeNow: Boolean, trackId: Long) {
            val soundInfo = viewModel.soundInfoLiveData.value
            soundInfo?.let {
                val track = it.trackInfo2TrackM()
                if (track != null && track.dataId == trackId) {
                    if (!EvaluateAlbumManager.shouldShowEvaluateDialogFootball()) {
                        if (!isLikeNow) AppCommentManager.showDialog(0, false)
                    }
                }
            }
        }

    }


    //收藏
    private val mCollectStateChange =
        TrackCollectManager.CollectListener { collectNow, trackId ->
            val soundInfo = viewModel.soundInfoLiveData.value
            soundInfo?.let {
                val otherInfo = it.otherInfo
                val track = it.trackInfo2TrackM()
                if (otherInfo != null && track != null && track.dataId == trackId) {
                    if (!collectNow) {
                        AppCommentManager.showDialog(0, false)
                    }
                }
            }
        }

    //订阅
    private val mAlbumFavoriteChange = object : AlbumEventManage.CollectListener {
        override fun onCollectChanged(collect: Boolean, id: Long) {
            XmPlayerManager.getInstance(context).updateSubscribeStatus(id, collect)
            val soundInfo = viewModel.soundInfoLiveData.value
            soundInfo?.let {
                val albumInfo = it.albumInfo
                if (albumInfo != null) {
                    if (albumInfo.isFavorite != collect && id == albumInfo.albumId) {
                        if (collect) {
                            albumInfo.subscribeCount = albumInfo.subscribeCount + 1
                        } else {
                            albumInfo.subscribeCount = albumInfo.subscribeCount - 1
                            if (albumInfo.subscribeCount < 0) {
                                albumInfo.subscribeCount = 0
                            }
                        }
                        albumInfo.isFavorite = collect
                        it.toAlbumM()?.isFavorite = collect
                        it.toAlbumM()?.favoriteCount = albumInfo.subscribeCount
                    }
                    mYNavigationBarComponent?.onSoundInfoLoaded(it)
                }
            }
        }

    }

    fun isLastTrackHasDoc(): Boolean {
        return mLastTrackHasDoc
    }

    fun setLastTrackHasDoc(lastTrackHasDoc: Boolean) {
        mLastTrackHasDoc = lastTrackHasDoc
    }

    override fun addUnableToPlayListener(listener: UnableToPlayListener) {
        mUnableToPlayStatusAggregator?.addUnableToPlayListener(listener)
    }

    override fun removeUnableToPlayListener(listener: UnableToPlayListener) {
        mUnableToPlayStatusAggregator?.removeUnableToPlayListener(listener)
    }

    override fun getCoverManager(): YCoverComponentsManager? {
        return coverComponentsManager
    }

    override fun getTopContainerHeightNormal(): Int {
        return YPlayHeight.NORMAL_HALF_SCREEN_HEIGHT.dp
    }

    override fun addCoverChangeListener(listener: CoverChangeListener) {
        mCoverChangeListenerSet?.add(listener)
    }

    override fun removeCoverChangeListener(listener: CoverChangeListener) {
        mCoverChangeListenerSet?.remove(listener)
    }

    override fun notifyCoverChangeListener(offset: Float) {
        mCoverChangeListenerSet?.forEach {
            it?.changeCoverTranslationY(offset)
        }
    }

    override fun onCoverLocationChange() {

    }

    override fun getTabNameForTrack(): String {
//        半屏播放|全屏播放|半屏视频|全屏视频|评论tab|详情|长文稿|PPT|绘本|时刻文稿
        return "半屏播放"
    }

    private val mPlayCommentFunctionNew: IPlayCommentFunctionNew =
        object : IPlayCommentFunctionNew {
            override fun isAllowComment(): Boolean {
                return false
            }

            override fun toggleInputBar(typeComment: Int) {

            }

            override fun toggleInputBar(
                type: Int,
                hint: String,
                parentCommentId: Long,
                inputHint: String
            ) {

            }

            override fun getAllowCommentType(): Int {
                return -1
            }

            override fun sendBullet(content: String, bulletColorType: Int, bottom: Boolean) {
                mXDanmakuStatusListeners?.forEach { it.addDanmaku(content,bulletColorType, bottom) }
            }
            override fun getSoundInfo(): PlayingSoundInfo {
                return viewModel.soundInfoLiveData.value!!
            }

            override fun tryShowRaiseDialog(type: Int): Boolean {
                return false
            }

            override fun setCommentCount(hotCount: Int, totalCount: Int) {
            }

            override fun setNewCommentCount(newCommentCount: Int) {}
            override fun showCommentSinglePage(needToAnchor: Boolean) {}
            override fun getCommentManager(): PlayCommentManagerNew {
                return mCommentManager!!
            }

            override fun setCommentEmptyState(
                isEmpty: Boolean,
                isNetError: Boolean,
                hasTalkList: Boolean) {}

        }

    override fun onDanmakuEntryOpenStatus(isOpened: Boolean) {
        viewModel.soundInfoLiveData.value?.let { DanmakuUtil.setDanmakuOpen(it, isOpened) }
        mXDanmakuStatusListeners?.forEach { it.onDanmakuEntryOpenStatusChange(isOpened) }
    }


    override fun getFunctionBarPositionY(): Int {
        return mFunctionEntriesComponentV2?.getViewLocationOnScreenPositionY() ?: 0
    }

    fun addScreenOrientationChangeListener(listener: IScreenOrientationChange) {
        mScreenOrientationChanges.add(listener)
    }


    ////////////////////////////////////////

    override fun openSoundDetail(jsonObject: JSONObject?) {
        val trackId = jsonObject?.optLong("trackId")?: 0L
        if (trackId != 0L && trackId != curTrack?.dataId) {
            return
        }
        val podcastResource = getBizArbiters()?.getBizResource(DynamicSizeFloating.PODCAST)
        if (podcastResource != null) {
            getBizArbiters()?.showFloating(DynamicSizeFloating.PODCAST, podcastResource, byClick = true)
        } else {
            getBizArbiters()?.showFloating(DynamicSizeFloating.SOUND_DETAIL, object :
                BizResource {
                override fun provideView() = null

                override fun provideFragment() = PlayTrackIntroDetailFragment(this@YPlayFragment) {
                    getBizArbiters()?.dismiss(DynamicSizeFloating.SOUND_DETAIL)
                }

                override fun onShow() {

                }

                override fun onHide() {

                }
            }, byClick = true)
        }

    }

    override fun openPlayRateList() {
        val track = curTrack
        if (TrackPlayQualityManager.getInstance().isFullDepth
            && OsUtil.isHarmonyVersionOver3()
            && track!=null
            && TrackPlayQualityManager.getInstance().hasFullDepSoundQuality(track)) {
            //鸿蒙3，全景声和倍速互斥
            val option = ToastOption()
            option.textGravity = Gravity.CENTER
            CustomToast.showToast(
                "全景声体验中，\n 不推荐倍速播放。\n 若需使用请先关闭全景声",
                ToastManager.LENGTH_LONG.toLong(),
                option
            )
            return
        }
        TempoManager.getInstance().showConfigDialog(context, true, true)
    }

    override fun openTimingOffPanel() {
        val soundInfo = viewModel.soundInfoLiveData.value
        var isSleep = false
        if (soundInfo?.otherInfo != null) {
            isSleep = soundInfo.otherInfo!!.isSleeping && KidSleepVipDialogManager.isSleepShowTime()
        }
        val fragment: PlanTerminateFragmentNewX
        if (isSleep) {
            val albumId = soundInfo?.albumInfo?.albumId
            val url: String? = soundInfo?.albumInfo?.coverLarge
            val categoryId = soundInfo?.albumInfo?.categoryId
            val title: String? = soundInfo?.trackInfo?.title
            val trackId = soundInfo?.trackInfo?.trackId

            // 哄睡模式下，跳转到新的哄睡设置半浮层
            val topActivity = BaseApplication.getTopActivity()
            if (topActivity is MainActivity) {
                ToolUtil.clickUrlAction(
                    topActivity,
                    "iting://open?msg_type=${AppConstants.PAGE_TO_KID_PUT_TO_BED_SETTING}&title=${title}&url=${url}&album_id=${albumId}&track_id=${trackId}&category_id=${categoryId}",
                    null
                )
                traceClickTimerOff(true, soundInfo)
                return
            }

            fragment = PlanTerminateFragmentNewX.newInstance(
                PlanTerminateFragmentNewX.TYPE_SLEEP,
                url,
                title
            )
            val service = PlayPageInternalServiceManager.getInstance().getService(
                ISkinAdShowCallBack::class.java
            )
            if (service != null) {
                val advertis = service.advertis
                fragment.setAd(advertis)
            }
            fragment.show(childFragmentManager, PlanTerminateFragmentNewX.TAG)
        } else {
            PlanTerminalNewDialog.show(
                childFragmentManager,
                "newPlay",
                soundInfo?.otherInfo?.alarmTip,
                soundInfo?.otherInfo?.alarmSubTips,
                soundInfo?.otherInfo?.alarmListenAwardUrl
            )
        }
        traceClickTimerOff(isSleep, soundInfo)
    }

    private fun traceClickTimerOff(isSleep: Boolean, soundInfo: PlayingSoundInfo?) {
        // 新声音播放页-定时关闭/哄睡模式  点击事件
        XMTraceApi.Trace()
            .click(17458) // 用户点击时上报
            .put("Item", if (isSleep) "哄睡模式" else "定时关闭")
            .put("currTrackId", curTrackId.toString())
            .put("currAlbumId", soundInfo?.albumInfo?.albumId?.toString()?: "")
            .put("currPage", "newPlay")
            .put("fullScreenMode", if (isFullScreen()) "full" else "half") // full 表示全屏，half 表示半屏
            .put("trackForm", if (isVideoMode()==false) "track" else "video") // track 表示音频，video 表示视频
            .put("playViewForm", "1") //  1 表示高唤起 2 表示低唤起
            .put("categoryId", "${soundInfo?.albumInfo?.categoryId ?: ""}")
            .put(
                XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                    XmRequestPage.PAGE_PLAY_PAGE))
            .apply {
                AudioInfoTraceUtil.interceptTrace(this)
            }
            .createTrace()
    }

    override fun openAudioEffectPanel() {
    }

    override fun openKidAiVoicePanel() {
        val dialog = KidAiSoundDialogXNew.newInstance()
        dialog.show(childFragmentManager, "KidAiSoundDialogXNew")
        MMKVUtil.getInstance()
            .saveBoolean(PreferenceConstantsInHost.KEY_IS_CLICK_AI_SOUND_ENTRY, true)
    }

    override fun openSharePanel() {
        mShareComponent?.performShare()
    }

    override fun openMonthTicketPanel() {

    }

    override fun getThemeData(trackId: Long): Theme? {
        if (viewModel.trackId == trackId) {
            //            TuneColorUtil.getColorString()
            return viewModel.currentTheme.value
        }
        return null
    }

    override fun getViewData(trackId: Long): PlayPageMinorData? {
        if (PlayPageMinorDataManager.getInstance().mCurTrackId == trackId) {
            return PlayPageMinorDataManager.getInstance().playPageMinorData
        }
        return null
    }

    override fun getTabsJsonData(trackId: Long): Any? {
        if (PlayPageDataManager.getInstance().curTrackId == trackId) {
            return PlayPageDataManager.getInstance().getJsonObject(PlayPageDataManager.getInstance().soundInfo)
        }
        return null
    }


    /**
     * {
     *   trackId : long long
     *   uid : long
     *   isFollowed : bool
     *   bizType  :  int
     *   subBizType :  long
     * }
     */
    override fun followAnchor(jsonObject: JSONObject, callback: (XResult) -> Unit) {
        try {
            val trackId = jsonObject.getLong("trackId")
            val uid = jsonObject.getLong("uid")
            val isFollowed = jsonObject.getBoolean("isFollowed")
            val bizType = jsonObject.getInt("bizType")
            val subBizType = jsonObject.getInt("subBizType")
            AnchorFollowManage.followV3(
                requireActivity(), uid, isFollowed, bizType, subBizType, object : IDataCallBack<Boolean> {
                override fun onSuccess(data: Boolean?) {
                    callback(XResult(true))
                }
                override fun onError(code: Int, message: String?) {
                    callback(XResult(false))
                }
            }, false)
        } catch (t: Throwable) {
            callback(XResult(false))
        }
    }

    override fun likeTrack(jsonObject: JSONObject, callback: (XResult) -> Unit) {
        try {
            val trackId = jsonObject.getLong("trackId")
            val isLike = jsonObject.getBoolean("isLike")

            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(activity, LoginByConstants.LOGIN_BY_LIKE)
                return
            }
            val activity = BaseApplication.getTopActivity() ?: return
            LikeTrackManage.loadLikeOrUnLikeRn(trackId, isLike, activity, object :IDataCallBack<Boolean?>{
                override fun onSuccess(data: Boolean?) {
                    callback(XResult(true))
                }

                override fun onError(code: Int, message: String?) {
                    callback(XResult(false))
                    CustomToast.showFailToast(message)
                }

            })
        } catch (t: Throwable) {
            callback(XResult(false))
        }
    }

    override fun collectTrack(jsonObject: JSONObject, callback: (XResult) -> Unit) {
        try {
            val trackId = jsonObject.getLong("trackId")
            val isCollect = jsonObject.getBoolean("isCollect")
            val soundInfo = curTrack
            if (soundInfo?.dataId == trackId) {
                TrackCollectManager.getInstance().requestCollectOrUnCollect(
                    isCollect, trackId,
                    object : IDataCallBack<Boolean?> {
                        override fun onSuccess(`object`: Boolean?) {
                            callback(XResult(true))
                        }

                        override fun onError(code: Int, message: String) {
                            callback(XResult(false))
                            CustomToast.showFailToast(message)
                        }
                    })
            } else {
                callback(XResult(false))
            }
        } catch (t: Throwable) {
            callback(XResult(false))
        }
    }

    /*
    albumId : long long
 anchorId : long
 albumTitle : string
 subscribeBizType : int
 followBizType : int
 followSubBizType : int
     */
    override fun subscribeAlbum(jsonObject: JSONObject, callback: (XResult) -> Unit) {
        try {
            val trackId = jsonObject.getLong("trackId")
            val albumId = jsonObject.getLong("albumId")
            val title = jsonObject.getString("albumTitle")
            val subscribeBizType = jsonObject.getInt("subscribeBizType")
            val followBizType = jsonObject.getInt("followBizType")
            val followSubBizType = jsonObject.getInt("followSubBizType")
            val isCollect = jsonObject.getBoolean("isCollect")
            val trackInfo = viewModel.soundInfoLiveData.value?.trackInfo
            if (trackInfo?.trackId == trackId) {
                PlayPageFollowUtil.albumFavorite(
                    albumId, title, isCollect,
                    subscribeBizType,
                    followBizType, followSubBizType,
                    object : IDataCallBack<Boolean> {
                        override fun onSuccess(data: Boolean?) {
                            callback(XResult(true))
                        }
                        override fun onError(code: Int, message: String?) {
                            callback(XResult(false))
                        }
                    })
            } else {
                callback(XResult(false))
            }
        } catch (t: Throwable) {
            callback(XResult(false))
        }
    }

    override fun download(jsonObject: JSONObject?) {
        PlayPageDownloadUtils.doDownload()
    }

    override fun getIting() = viewModel.iting

    override fun getRightColor(): Int {
        return viewModel.getThemeColor()
    }

    override fun isYPlayPage(): Boolean {
        return true
    }

    override fun pauseVideoPlay() {
        if (videoComponent?.isPlaying() == true) {
            videoComponent?.playOrPause()
        }
    }

    override fun showFragmentOnPlayPage(fragment: Fragment?) {
        playPageApiForTab?.showFragmentOnPlayPage(fragment)
    }

    override fun hideFragmentOnPlayPage(fragment: Fragment?) {
        playPageApiForTab?.hideFragmentOnPlayPage(fragment)
    }

    companion object {
        @JvmOverloads
        @JvmStatic
        fun showSoundEffectDialog(
            expandSleepItem: Boolean = false,
            focusTab: String = TAB_EFFECT,
            soundInfo: PlayingSoundInfo? = PlayPageDataManager.getInstance().getSoundInfo()
        ): ChooseTrackSoundEffectAiDialogXNew? {
            if (soundInfo == null) return null
            val activity = BaseApplication.getTopActivity()
            if (activity is MainActivity) {
                val fragment = activity.getCurrentTopFragment()
                if (fragment is BaseFragment2) {
                    if (!checkToShowSHQPrivilegeFloatDialog(
                            soundInfo,
                            fragment as BaseFragment2?,
                            false
                        )
                    ) {
                        return ChooseTrackSoundEffectAiDialogXNew.newInstance(
                            false,
                            expandSleepItem,
                            focusTab
                        ).apply {
                            show(
                                activity.supportFragmentManager,
                                "ChooseTrackSoundEffectAiDialogXNew"
                            )
                        }
                    }
                }
            }
            return null
        }
    }


    override fun openUri(uri: String) {
        val activity = BaseApplication.getTopActivity() as? MainActivity
        ToolUtil.clickUrlAction(activity, uri, null)
    }

    private fun checkShowAwardHintDialog() {
        if (!isRealVisable) {
            return
        }
        val soundInfo = viewModel.soundInfoLiveData.value ?: return
        val data = soundInfo.resourceMap?.get("playPageLayer")?.find { it.bizType == "ListenAward" } ?: return
        if (!MmkvCommonUtil.getInstance(mContext).getBoolean(PreferenceConstantsInMain.KEY_CAN_SHOW_AWARD_DIALOG_IN_PLAY_PAGE, true)) {
            return
        }
        val maxShowTimesPerWeek = ConfigureCenter.getInstance().getInt(CConstants.Group_toc.GROUP_NAME,
            "listen_to_reward_reminders", 1)
        if (maxShowTimesPerWeek <= 0) {
            return
        }
        val lastShowAwardDialogTime = MmkvCommonUtil.getInstance(mContext).getLong(PreferenceConstantsInMain.KEY_LAST_SHOW_AWARD_DIALOG_TIME_IN_PLAY_PAGE, 0)
        if (lastShowAwardDialogTime > 0) {
            val lastShowCalender = Calendar.getInstance()
            lastShowCalender.timeInMillis = lastShowAwardDialogTime
            val curCalendar = Calendar.getInstance()
            if (DateUtils.isSameDay(curCalendar, lastShowCalender)) { // 每天最多出1次
                return
            }
            val lastIgnoreTime = MmkvCommonUtil.getInstance(mContext).getLong(
                PreferenceConstantsInMain.KEY_LAST_IGNORE_AWARD_DIALOG_TS, 0)
            if (lastIgnoreTime > 0 && System.currentTimeMillis() - lastIgnoreTime < 7 * 24 * 3600 * 1000) { // 忽略过一次，7天内不再弹出
                return
            }
            if (DateUtils.isSameWeek(lastShowCalender, curCalendar)) {
                val restShowTimesThisWeek = MmkvCommonUtil.getInstance(mContext).getInt(
                    PreferenceConstantsInMain.KEY_AWARD_DIALOG_REST_SHOW_TIMES_THIS_WEEK, maxShowTimesPerWeek)
                if (restShowTimesThisWeek <= 0) { // 每周展示次数额度用完了，不能继续展示
                    return
                } else {
                    MmkvCommonUtil.getInstance(mContext).saveInt(PreferenceConstantsInMain.KEY_AWARD_DIALOG_REST_SHOW_TIMES_THIS_WEEK,
                        restShowTimesThisWeek - 1)
                }
            } else { // 跟上次展示时间不在同一周，可展示
                MmkvCommonUtil.getInstance(mContext).saveInt(PreferenceConstantsInMain.KEY_AWARD_DIALOG_REST_SHOW_TIMES_THIS_WEEK,
                    maxShowTimesPerWeek - 1)
            }
        } else { // 未展示过弹框，可展示
            MmkvCommonUtil.getInstance(mContext).saveInt(PreferenceConstantsInMain.KEY_AWARD_DIALOG_REST_SHOW_TIMES_THIS_WEEK,
                maxShowTimesPerWeek - 1)
        }
        val curTimeMs = System.currentTimeMillis()
        MmkvCommonUtil.getInstance(mContext).saveLong(PreferenceConstantsInMain.KEY_LAST_SHOW_AWARD_DIALOG_TIME_IN_PLAY_PAGE, curTimeMs)
        val bundle = Bundle()
        bundle.putString("bundle", "playpage_listening_reward_popup")
        bundle.putBoolean("canSlide", false)
        bundle.putString("transparent", "1")
        bundle.putLong("trackId", soundInfo.trackInfo?.trackId ?: 0)
        bundle.putLong("albumId", soundInfo.albumInfo?.albumId ?: 0)
        data.mapToBundle(bundle)
        mHasClickedClaimReward = false
        mRewardHintDialog = ReactNativeFloatingDialog.show(
            this,
            bundle, BaseUtil.dp2px(mContext, 600f),
            object : IRNFunctionRouter.IDataListener {
                override fun onDataReceived(data: HashMap<String, Any>?) {
                    if (data == null) {
                        return
                    }
                    if ("close" == data["action"]) {
                        mRewardHintDialog?.dismiss()
                    } else if ("click_nomore_remind" == data["action"]) {
                        MmkvCommonUtil.getInstance(mContext).saveBoolean(PreferenceConstantsInMain.KEY_CAN_SHOW_AWARD_DIALOG_IN_PLAY_PAGE, false)
                    } else if ("click_claim_reward" == data["action"]) {
                        mHasClickedClaimReward = true
                        MmkvCommonUtil.getInstance(mContext).saveLong(PreferenceConstantsInMain.KEY_LAST_IGNORE_AWARD_DIALOG_TS, 0)
                    }
                }
                override fun requireData(): Bundle? = null
            }
        )
        mRewardHintDialog?.mCanceledOnTouchOutside = true
        mRewardHintDialog?.setOnDismissListener {
            if (!mHasClickedClaimReward) {
                if (MmkvCommonUtil.getInstance(mContext).getLong(PreferenceConstantsInMain.KEY_LAST_IGNORE_AWARD_DIALOG_TS, 0) > 0) { // 连续两次直接取消，以后不再弹出
                    MmkvCommonUtil.getInstance(mContext).saveBoolean(PreferenceConstantsInMain.KEY_CAN_SHOW_AWARD_DIALOG_IN_PLAY_PAGE, false)
                } else {
                    MmkvCommonUtil.getInstance(mContext).saveLong(PreferenceConstantsInMain.KEY_LAST_IGNORE_AWARD_DIALOG_TS, System.currentTimeMillis())
                }
            }
        }
    }
}
